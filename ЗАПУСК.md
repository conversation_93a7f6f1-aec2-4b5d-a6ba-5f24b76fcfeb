# 🚀 Інструкції для запуску гри "Хімічні Елементи"

## Швидкий старт

### Варіант 1: Пряме відкриття (рекомендовано для тестування)
1. Відкрийте файл `index.html` у веб-браузері
2. Або відкрийте `test.html` для тестування компонентів

### Варіант 2: Локальний HTTP сервер (рекомендовано для повної функціональності)

#### Python (якщо встановлений)
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

#### Node.js
```bash
# Встановлення http-server глобально
npm install -g http-server

# Запуск сервера
http-server -p 8000 -c-1
```

#### PHP
```bash
php -S localhost:8000
```

#### Live Server (VS Code)
1. Встановіть розширення "Live Server"
2. Клікніть правою кнопкою на `index.html`
3. Виберіть "Open with Live Server"

## Тестування

### Автоматичні тести
1. Відкрийте `test.html` у браузері
2. Натисніть кнопки тестування по черзі:
   - "Тест модулів" - перевірка завантаження
   - "Тест Grid" - тестування ігрової логіки
   - "Тест Audio" - перевірка аудіо системи
   - "Тест Renderer" - перевірка графіки
   - "Тест Storage" - перевірка збереження
   - "Завантажити гру" - повний запуск

### Ручне тестування
1. Відкрийте `index.html`
2. Дочекайтеся завантаження гри
3. Спробуйте основні функції:
   - Переміщення елементів
   - Створення збігів
   - Використання кнопок інтерфейсу

## Можливі проблеми та рішення

### Проблема: Модулі не завантажуються
**Рішення**: Використовуйте HTTP сервер замість прямого відкриття файлу

### Проблема: Аудіо не працює
**Рішення**: 
- Переконайтеся що браузер підтримує Web Audio API
- Дозвольте автовідтворення аудіо в налаштуваннях браузера
- Клікніть на сторінці для активації аудіо контексту

### Проблема: Гра не відображається
**Рішення**:
- Перевірте консоль браузера на помилки (F12)
- Переконайтеся що всі файли присутні
- Спробуйте оновити сторінку (Ctrl+F5)

### Проблема: Сенсорне управління не працює
**Рішення**:
- Відкрийте на мобільному пристрої або використовуйте емуляцію
- Переконайтеся що браузер підтримує touch events

## Підтримувані браузери

### Десктоп
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

### Мобільні
- ✅ Chrome Mobile 90+
- ✅ Safari iOS 14+
- ✅ Samsung Internet 14+
- ✅ Firefox Mobile 88+

## Структура файлів

```
/
├── index.html          # Головна сторінка гри
├── test.html           # Сторінка тестування
├── manifest.json       # PWA маніфест
├── sw.js              # Service Worker
├── README.md          # Документація
├── ЗАПУСК.md          # Ця інструкція
├── libs/              # Локальні бібліотеки
│   ├── tone.min.js
│   ├── tinycolor.min.js
│   ├── gsap.min.js
│   └── dexie.min.js
├── src/               # Вихідний код
│   ├── core/         # Основна логіка
│   ├── graphics/     # Графіка та рендеринг
│   ├── audio/        # Аудіо система
│   ├── ui/           # Інтерфейс користувача
│   └── data/         # Збереження даних
└── tests/            # Тести
    └── grid.test.js
```

## Особливості гри

### Унікальні механіки
- **Хімічні реакції**: Елементи взаємодіють між собою
- **Динамічна гравітація**: Напрямок змінюється кожні 45 секунд
- **Процедурна музика**: Адаптується до стану гри
- **Каскадні збіги**: Ланцюгові реакції

### Управління
- **Миша**: Клік та перетягування
- **Дотик**: Тап та свайп
- **Клавіатура**: Стрілки для навігації
- **Жести**: Обертання для зміни гравітації

### Клавіатурні скорочення
- `Пробіл/Escape` - Пауза
- `H` - Підказка
- `S` - Перемішати
- `M` - Вимкнути звук

## Налаштування продуктивності

### Для слабких пристроїв
1. Відкрийте консоль браузера (F12)
2. Виконайте:
```javascript
// Зменшення кількості частинок
window.game.renderer.particleSystem.maxParticles = 100;

// Зменшення якості аудіо
window.game.soundManager.setVolume('sfx', 0.3);
```

### Для потужних пристроїв
```javascript
// Збільшення кількості частинок
window.game.renderer.particleSystem.maxParticles = 1000;

// Увімкнення додаткових ефектів
window.game.renderer.enableAdvancedEffects = true;
```

## Розробка та налагодження

### Консольні команди
```javascript
// Інформація про гру
window.game.getGameInfo();

// Запуск тестів
window.runGridTests();

// Зміна рівня
window.game.level = 5;

// Додавання очок
window.game.score += 1000;

// Зміна гравітації
window.game.grid.gravityDirection = 'left';
```

### Налагодження
1. Відкрийте Developer Tools (F12)
2. Перейдіть на вкладку Console
3. Перевірте повідомлення про помилки
4. Використовуйте `console.log()` для відстеження

## Встановлення як PWA

### Chrome/Edge
1. Відкрийте гру в браузері
2. Натисніть іконку "Встановити" в адресному рядку
3. Підтвердіть встановлення

### Safari iOS
1. Відкрийте гру в Safari
2. Натисніть кнопку "Поділитися"
3. Виберіть "Додати на головний екран"

### Android
1. Відкрийте гру в Chrome
2. Натисніть меню (три крапки)
3. Виберіть "Додати на головний екран"

## Підтримка

Якщо виникли проблеми:
1. Перевірте консоль браузера на помилки
2. Переконайтеся що всі файли присутні
3. Спробуйте інший браузер
4. Очистіть кеш браузера

---

**Версія**: 1.0.0  
**Створено**: Augment Agent  
**Дата**: Серпень 2025
