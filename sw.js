/**
 * Service Worker для PWA функціональності
 * Забезпечує офлайн роботу та кешування ресурсів
 */

const CACHE_NAME = 'chemical-elements-v1.0.0';
const STATIC_CACHE_NAME = 'chemical-elements-static-v1.0.0';
const DYNAMIC_CACHE_NAME = 'chemical-elements-dynamic-v1.0.0';

// Ресурси для кешування
const STATIC_ASSETS = [
    '/',
    '/index.html',
    '/manifest.json',
    '/libs/tone.min.js',
    '/libs/tinycolor.min.js',
    '/libs/gsap.min.js',
    '/libs/dexie.min.js',
    '/src/core/Game.js',
    '/src/core/Grid.js',
    '/src/core/Rules.js',
    '/src/graphics/Renderer.js',
    '/src/audio/SoundManager.js',
    '/src/ui/Interface.js',
    '/src/ui/Input.js',
    '/src/data/Storage.js'
];

// Ресурси які не потрібно кешувати
const EXCLUDE_FROM_CACHE = [
    '/api/',
    '/analytics/',
    'chrome-extension://',
    'moz-extension://'
];

/**
 * Подія встановлення Service Worker
 */
self.addEventListener('install', (event) => {
    console.log('Service Worker встановлюється...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE_NAME)
            .then((cache) => {
                console.log('Кешування статичних ресурсів...');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('Статичні ресурси закешовано');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('Помилка кешування статичних ресурсів:', error);
            })
    );
});

/**
 * Подія активації Service Worker
 */
self.addEventListener('activate', (event) => {
    console.log('Service Worker активується...');
    
    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        // Видалення старих кешів
                        if (cacheName !== STATIC_CACHE_NAME && 
                            cacheName !== DYNAMIC_CACHE_NAME &&
                            cacheName.startsWith('chemical-elements-')) {
                            console.log('Видалення старого кешу:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker активовано');
                return self.clients.claim();
            })
    );
});

/**
 * Обробка запитів (fetch)
 */
self.addEventListener('fetch', (event) => {
    const requestUrl = new URL(event.request.url);
    
    // Ігнорування запитів які не потрібно кешувати
    if (EXCLUDE_FROM_CACHE.some(pattern => requestUrl.href.includes(pattern))) {
        return;
    }
    
    // Стратегія кешування залежно від типу ресурсу
    if (STATIC_ASSETS.includes(requestUrl.pathname) || requestUrl.pathname === '/') {
        // Cache First для статичних ресурсів
        event.respondWith(cacheFirstStrategy(event.request));
    } else {
        // Network First для динамічних ресурсів
        event.respondWith(networkFirstStrategy(event.request));
    }
});

/**
 * Стратегія Cache First
 */
async function cacheFirstStrategy(request) {
    try {
        // Спроба отримання з кешу
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Якщо немає в кеші, завантажуємо з мережі
        const networkResponse = await fetch(request);
        
        // Кешування відповіді
        if (networkResponse.status === 200) {
            const cache = await caches.open(STATIC_CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
        
    } catch (error) {
        console.error('Помилка Cache First стратегії:', error);
        
        // Fallback на офлайн сторінку
        if (request.destination === 'document') {
            return caches.match('/index.html');
        }
        
        throw error;
    }
}

/**
 * Стратегія Network First
 */
async function networkFirstStrategy(request) {
    try {
        // Спроба завантаження з мережі
        const networkResponse = await fetch(request);
        
        // Кешування успішної відповіді
        if (networkResponse.status === 200) {
            const cache = await caches.open(DYNAMIC_CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
        
    } catch (error) {
        console.log('Мережа недоступна, використовуємо кеш');
        
        // Fallback на кеш
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        throw error;
    }
}

/**
 * Обробка повідомлень від основного потоку
 */
self.addEventListener('message', (event) => {
    const { type, data } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'CACHE_URLS':
            cacheUrls(data.urls);
            break;
            
        case 'CLEAR_CACHE':
            clearCache(data.cacheName);
            break;
            
        case 'GET_CACHE_SIZE':
            getCacheSize().then(size => {
                event.ports[0].postMessage({ type: 'CACHE_SIZE', size });
            });
            break;
    }
});

/**
 * Кешування додаткових URL
 */
async function cacheUrls(urls) {
    try {
        const cache = await caches.open(DYNAMIC_CACHE_NAME);
        await cache.addAll(urls);
        console.log('Додаткові URL закешовано');
    } catch (error) {
        console.error('Помилка кешування додаткових URL:', error);
    }
}

/**
 * Очищення конкретного кешу
 */
async function clearCache(cacheName) {
    try {
        const deleted = await caches.delete(cacheName || DYNAMIC_CACHE_NAME);
        console.log(`Кеш ${cacheName} ${deleted ? 'очищено' : 'не знайдено'}`);
    } catch (error) {
        console.error('Помилка очищення кешу:', error);
    }
}

/**
 * Отримання розміру кешу
 */
async function getCacheSize() {
    try {
        const cacheNames = await caches.keys();
        let totalSize = 0;
        
        for (const cacheName of cacheNames) {
            if (cacheName.startsWith('chemical-elements-')) {
                const cache = await caches.open(cacheName);
                const keys = await cache.keys();
                
                for (const request of keys) {
                    const response = await cache.match(request);
                    if (response) {
                        const blob = await response.blob();
                        totalSize += blob.size;
                    }
                }
            }
        }
        
        return totalSize;
        
    } catch (error) {
        console.error('Помилка розрахунку розміру кешу:', error);
        return 0;
    }
}

/**
 * Обробка помилок
 */
self.addEventListener('error', (event) => {
    console.error('Помилка Service Worker:', event.error);
});

/**
 * Обробка необроблених відхилень Promise
 */
self.addEventListener('unhandledrejection', (event) => {
    console.error('Необроблене відхилення в Service Worker:', event.reason);
});

console.log('Service Worker завантажено');
