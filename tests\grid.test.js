/**
 * Тести для класу Grid
 * Перевірка основної функціональності ігрової сітки
 */

// Імпорт модулів для тестування
import { Grid } from '../src/core/Grid.js';

/**
 * Простий тестовий фреймворк
 */
class TestFramework {
    constructor() {
        this.tests = [];
        this.passed = 0;
        this.failed = 0;
    }
    
    test(name, testFunction) {
        this.tests.push({ name, testFunction });
    }
    
    async run() {
        console.log('🧪 Запуск тестів Grid...\n');
        
        for (const test of this.tests) {
            try {
                await test.testFunction();
                console.log(`✅ ${test.name}`);
                this.passed++;
            } catch (error) {
                console.error(`❌ ${test.name}: ${error.message}`);
                this.failed++;
            }
        }
        
        console.log(`\n📊 Результати: ${this.passed} пройдено, ${this.failed} провалено`);
        
        if (this.failed === 0) {
            console.log('🎉 Всі тести пройдено успішно!');
        }
    }
    
    assert(condition, message) {
        if (!condition) {
            throw new Error(message || 'Assertion failed');
        }
    }
    
    assertEqual(actual, expected, message) {
        if (actual !== expected) {
            throw new Error(message || `Expected ${expected}, got ${actual}`);
        }
    }
    
    assertNotNull(value, message) {
        if (value === null || value === undefined) {
            throw new Error(message || 'Value should not be null or undefined');
        }
    }
}

// Створення екземпляру тестового фреймворку
const test = new TestFramework();

/**
 * Тест ініціалізації сітки
 */
test.test('Ініціалізація сітки', async () => {
    const grid = new Grid(8, 6);
    await grid.initialize();
    
    test.assertEqual(grid.size, 8, 'Розмір сітки має бути 8');
    test.assertEqual(grid.elementTypes, 6, 'Кількість типів елементів має бути 6');
    test.assertNotNull(grid.cells, 'Клітинки мають бути ініціалізовані');
    test.assertEqual(grid.cells.length, 8, 'Кількість рядків має бути 8');
    test.assertEqual(grid.cells[0].length, 8, 'Кількість стовпців має бути 8');
});

/**
 * Тест генерації нової сітки
 */
test.test('Генерація нової сітки', async () => {
    const grid = new Grid(8, 6);
    await grid.initialize();
    await grid.generateNewGrid();
    
    // Перевірка що всі клітинки заповнені
    for (let x = 0; x < grid.size; x++) {
        for (let y = 0; y < grid.size; y++) {
            test.assertNotNull(grid.cells[x][y].element, `Клітинка (${x}, ${y}) має містити елемент`);
        }
    }
    
    // Перевірка що немає початкових збігів
    let hasInitialMatches = false;
    for (let x = 0; x < grid.size; x++) {
        for (let y = 0; y < grid.size; y++) {
            if (grid.hasMatchAt(x, y)) {
                hasInitialMatches = true;
                break;
            }
        }
    }
    
    test.assert(!hasInitialMatches, 'Не повинно бути початкових збігів');
});

/**
 * Тест валідації позицій
 */
test.test('Валідація позицій', async () => {
    const grid = new Grid(8, 6);
    await grid.initialize();
    
    // Валідні позиції
    test.assert(grid.isValidPosition(0, 0), 'Позиція (0,0) має бути валідною');
    test.assert(grid.isValidPosition(7, 7), 'Позиція (7,7) має бути валідною');
    test.assert(grid.isValidPosition(3, 4), 'Позиція (3,4) має бути валідною');
    
    // Невалідні позиції
    test.assert(!grid.isValidPosition(-1, 0), 'Позиція (-1,0) має бути неvalідною');
    test.assert(!grid.isValidPosition(0, -1), 'Позиція (0,-1) має бути неvalідною');
    test.assert(!grid.isValidPosition(8, 0), 'Позиція (8,0) має бути неvalідною');
    test.assert(!grid.isValidPosition(0, 8), 'Позиція (0,8) має бути неvalідною');
});

/**
 * Тест перевірки сусідності
 */
test.test('Перевірка сусідності', async () => {
    const grid = new Grid(8, 6);
    await grid.initialize();
    
    // Сусідні клітинки
    test.assert(grid.areAdjacent(0, 0, 0, 1), 'Клітинки (0,0) та (0,1) мають бути сусідніми');
    test.assert(grid.areAdjacent(0, 0, 1, 0), 'Клітинки (0,0) та (1,0) мають бути сусідніми');
    test.assert(grid.areAdjacent(3, 3, 3, 4), 'Клітинки (3,3) та (3,4) мають бути сусідніми');
    test.assert(grid.areAdjacent(3, 3, 4, 3), 'Клітинки (3,3) та (4,3) мають бути сусідніми');
    
    // Несусідні клітинки
    test.assert(!grid.areAdjacent(0, 0, 1, 1), 'Клітинки (0,0) та (1,1) не мають бути сусідніми');
    test.assert(!grid.areAdjacent(0, 0, 0, 2), 'Клітинки (0,0) та (0,2) не мають бути сусідніми');
    test.assert(!grid.areAdjacent(0, 0, 2, 0), 'Клітинки (0,0) та (2,0) не мають бути сусідніми');
});

/**
 * Тест отримання сусідів
 */
test.test('Отримання сусідів', async () => {
    const grid = new Grid(8, 6);
    await grid.initialize();
    
    // Кутова клітинка
    const cornersNeighbors = grid.getNeighbors(0, 0);
    test.assertEqual(cornersNeighbors.length, 2, 'Кутова клітинка має мати 2 сусідів');
    
    // Крайова клітинка
    const edgeNeighbors = grid.getNeighbors(0, 3);
    test.assertEqual(edgeNeighbors.length, 3, 'Крайова клітинка має мати 3 сусідів');
    
    // Центральна клітинка
    const centerNeighbors = grid.getNeighbors(3, 3);
    test.assertEqual(centerNeighbors.length, 4, 'Центральна клітинка має мати 4 сусідів');
});

/**
 * Тест зміни напрямку гравітації
 */
test.test('Зміна напрямку гравітації', async () => {
    const grid = new Grid(8, 6);
    await grid.initialize();
    
    // Початковий напрямок
    test.assertEqual(grid.gravityDirection, 'down', 'Початковий напрямок гравітації має бути "down"');
    
    // Зміна напрямку
    grid.rotateGravity();
    test.assertEqual(grid.gravityDirection, 'left', 'Після першого повороту має бути "left"');
    
    grid.rotateGravity();
    test.assertEqual(grid.gravityDirection, 'up', 'Після другого повороту має бути "up"');
    
    grid.rotateGravity();
    test.assertEqual(grid.gravityDirection, 'right', 'Після третього повороту має бути "right"');
    
    grid.rotateGravity();
    test.assertEqual(grid.gravityDirection, 'down', 'Після четвертого повороту має бути "down"');
});

/**
 * Тест додавання нових типів елементів
 */
test.test('Додавання нових типів елементів', async () => {
    const grid = new Grid(8, 4);
    await grid.initialize();
    
    test.assertEqual(grid.elementTypes, 4, 'Початкова кількість типів має бути 4');
    
    grid.addNewElementType();
    test.assertEqual(grid.elementTypes, 5, 'Після додавання має бути 5 типів');
    
    // Перевірка максимуму
    grid.elementTypes = 8;
    grid.addNewElementType();
    test.assertEqual(grid.elementTypes, 8, 'Не має перевищувати максимум типів');
});

/**
 * Тест збереження та завантаження стану
 */
test.test('Збереження та завантаження стану', async () => {
    const grid = new Grid(8, 6);
    await grid.initialize();
    await grid.generateNewGrid();
    
    // Збереження стану
    const state = grid.getState();
    test.assertNotNull(state, 'Стан має бути збережений');
    test.assertNotNull(state.cells, 'Стан має містити клітинки');
    test.assertEqual(state.gravityDirection, 'down', 'Стан має містити напрямок гравітації');
    
    // Створення нової сітки та завантаження стану
    const newGrid = new Grid(8, 6);
    await newGrid.initialize();
    await newGrid.loadState(state);
    
    // Перевірка що стан відновлено
    test.assertEqual(newGrid.gravityDirection, state.gravityDirection, 'Напрямок гравітації має бути відновлений');
    test.assertEqual(newGrid.elementTypes, state.elementTypes, 'Кількість типів елементів має бути відновлена');
});

/**
 * Тест хімічних елементів
 */
test.test('Хімічні елементи та реакції', async () => {
    const grid = new Grid(8, 6);
    await grid.initialize();
    
    // Перевірка наявності елементів
    test.assertNotNull(grid.elements.fire, 'Елемент вогню має існувати');
    test.assertNotNull(grid.elements.water, 'Елемент води має існувати');
    test.assertNotNull(grid.elements.earth, 'Елемент землі має існувати');
    
    // Перевірка реакцій
    test.assertNotNull(grid.elements.fire.reactions.water, 'Вогонь має реагувати з водою');
    test.assertEqual(grid.elements.fire.reactions.water, 'steam', 'Вогонь + вода = пара');
    
    // Перевірка масиву елементів
    test.assert(grid.elementArray.length >= 6, 'Має бути принаймні 6 елементів');
});

/**
 * Запуск всіх тестів
 */
if (typeof window !== 'undefined') {
    // Запуск в браузері
    window.runGridTests = async () => {
        await test.run();
    };
    
    console.log('Тести Grid готові. Викличте window.runGridTests() для запуску.');
} else {
    // Запуск в Node.js
    test.run();
}
