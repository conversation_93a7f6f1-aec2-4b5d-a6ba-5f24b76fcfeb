/**
 * Основний клас гри "Хімічні Елементи"
 * Управляє всіма аспектами гри та координує роботу підсистем
 */

import { Grid } from './Grid.js';
import { Rules } from './Rules.js';
import { Renderer } from '../graphics/Renderer.js';
import { SoundManager } from '../audio/SoundManager.js';
import { Interface } from '../ui/Interface.js';
import { Input } from '../ui/Input.js';
import { Storage } from '../data/Storage.js';

export class Game {
    constructor() {
        // Основні компоненти гри
        this.grid = null;
        this.rules = null;
        this.renderer = null;
        this.soundManager = null;
        this.interface = null;
        this.input = null;
        this.storage = null;
        
        // Стан гри
        this.gameState = 'loading'; // loading, playing, paused, gameOver
        this.score = 0;
        this.level = 1;
        this.moves = 30;
        this.targetScore = 1000;
        
        // Налаштування гри
        this.config = {
            gridSize: 8,
            elementTypes: 6,
            moveLimit: 30,
            scoreMultiplier: 10,
            comboBonus: 50,
            chemistryBonus: 100
        };
        
        // Таймери та анімації
        this.lastUpdateTime = 0;
        this.animationFrame = null;
        this.isPaused = false;
        
        // Прив'язка методів до контексту
        this.update = this.update.bind(this);
        this.handleResize = this.handleResize.bind(this);
    }
    
    /**
     * Ініціалізація гри та всіх підсистем
     */
    async initialize() {
        try {
            console.log('Початок ініціалізації гри...');
            
            // Ініціалізація системи збереження
            this.storage = new Storage();
            await this.storage.initialize();
            
            // Завантаження збережених налаштувань
            const savedConfig = await this.storage.getSettings();
            if (savedConfig) {
                Object.assign(this.config, savedConfig);
            }
            
            // Ініціалізація графічної системи
            this.renderer = new Renderer();
            await this.renderer.initialize();
            
            // Ініціалізація аудіо системи
            this.soundManager = new SoundManager();
            await this.soundManager.initialize();
            
            // Ініціалізація ігрової сітки
            this.grid = new Grid(this.config.gridSize, this.config.elementTypes);
            await this.grid.initialize();
            
            // Ініціалізація правил гри
            this.rules = new Rules(this.grid, this.config);
            
            // Ініціалізація інтерфейсу
            this.interface = new Interface(this);
            await this.interface.initialize();
            
            // Ініціалізація системи вводу
            this.input = new Input(this);
            await this.input.initialize();
            
            // Налаштування розміру canvas
            this.handleResize();
            
            // Завантаження збереженої гри або початок нової
            const savedGame = await this.storage.getGameState();
            if (savedGame && savedGame.level > 1) {
                await this.loadGame(savedGame);
            } else {
                await this.startNewGame();
            }
            
            // Запуск основного циклу гри
            this.gameState = 'playing';
            this.startGameLoop();
            
            console.log('Гра успішно ініціалізована');
            
        } catch (error) {
            console.error('Помилка ініціалізації гри:', error);
            this.gameState = 'error';
            throw error;
        }
    }
    
    /**
     * Початок нової гри
     */
    async startNewGame() {
        console.log('Початок нової гри');
        
        // Скидання статистики
        this.score = 0;
        this.level = 1;
        this.moves = this.config.moveLimit;
        this.targetScore = 1000;
        
        // Генерація нової сітки
        await this.grid.generateNewGrid();
        
        // Оновлення інтерфейсу
        this.interface.updateStats({
            score: this.score,
            level: this.level,
            moves: this.moves,
            targetScore: this.targetScore
        });
        
        // Збереження початкового стану
        await this.saveGame();
        
        // Відтворення початкової музики
        this.soundManager.playBackgroundMusic();
    }
    
    /**
     * Завантаження збереженої гри
     */
    async loadGame(savedState) {
        console.log('Завантаження збереженої гри');
        
        this.score = savedState.score || 0;
        this.level = savedState.level || 1;
        this.moves = savedState.moves || this.config.moveLimit;
        this.targetScore = savedState.targetScore || 1000;
        
        // Відновлення стану сітки
        if (savedState.gridState) {
            await this.grid.loadState(savedState.gridState);
        } else {
            await this.grid.generateNewGrid();
        }
        
        // Оновлення інтерфейсу
        this.interface.updateStats({
            score: this.score,
            level: this.level,
            moves: this.moves,
            targetScore: this.targetScore
        });
    }
    
    /**
     * Збереження поточного стану гри
     */
    async saveGame() {
        const gameState = {
            score: this.score,
            level: this.level,
            moves: this.moves,
            targetScore: this.targetScore,
            gridState: this.grid.getState(),
            timestamp: Date.now()
        };
        
        await this.storage.saveGameState(gameState);
    }
    
    /**
     * Основний цикл гри
     */
    startGameLoop() {
        const gameLoop = (currentTime) => {
            if (this.gameState === 'playing' && !this.isPaused) {
                this.update(currentTime);
                this.render();
            }
            
            this.animationFrame = requestAnimationFrame(gameLoop);
        };
        
        this.animationFrame = requestAnimationFrame(gameLoop);
    }
    
    /**
     * Оновлення логіки гри
     */
    update(currentTime) {
        const deltaTime = currentTime - this.lastUpdateTime;
        this.lastUpdateTime = currentTime;
        
        // Оновлення сітки (анімації, падіння елементів)
        this.grid.update(deltaTime);
        
        // Перевірка на можливі ходи
        if (this.moves <= 0 && !this.grid.hasAnimations()) {
            this.checkGameEnd();
        }
        
        // Автоматичне збереження кожні 10 секунд
        if (Math.floor(currentTime / 10000) > Math.floor((currentTime - deltaTime) / 10000)) {
            this.saveGame();
        }
    }
    
    /**
     * Рендеринг гри
     */
    render() {
        this.renderer.clear();
        this.renderer.renderGrid(this.grid);
        this.renderer.renderEffects();
        this.renderer.present();
    }
    
    /**
     * Обробка ходу гравця
     */
    async makeMove(fromX, fromY, toX, toY) {
        if (this.gameState !== 'playing' || this.moves <= 0) {
            return false;
        }
        
        // Перевірка валідності ходу
        if (!this.rules.isValidMove(fromX, fromY, toX, toY)) {
            this.soundManager.playSound('invalidMove');
            return false;
        }
        
        // Виконання ходу
        const moveResult = await this.grid.makeMove(fromX, fromY, toX, toY);
        
        if (moveResult.success) {
            this.moves--;
            
            // Обчислення очок за хід
            const points = this.rules.calculateScore(moveResult);
            this.score += points;
            
            // Відтворення звуку
            this.soundManager.playSound('match', moveResult.matchType);
            
            // Оновлення інтерфейсу
            this.interface.updateStats({
                score: this.score,
                moves: this.moves
            });
            
            // Перевірка на перехід на наступний рівень
            if (this.score >= this.targetScore) {
                await this.levelUp();
            }
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Перехід на наступний рівень
     */
    async levelUp() {
        this.level++;
        this.moves = this.config.moveLimit;
        this.targetScore = Math.floor(this.targetScore * 1.5);
        
        // Збільшення складності
        if (this.level % 5 === 0 && this.config.elementTypes < 8) {
            this.config.elementTypes++;
            this.grid.addNewElementType();
        }
        
        // Генерація нової сітки
        await this.grid.generateNewGrid();
        
        // Оновлення інтерфейсу
        this.interface.updateStats({
            level: this.level,
            moves: this.moves,
            targetScore: this.targetScore
        });
        
        // Відтворення звуку переходу на рівень
        this.soundManager.playSound('levelUp');
        
        // Збереження прогресу
        await this.saveGame();
        
        console.log(`Перехід на рівень ${this.level}`);
    }
    
    /**
     * Перевірка завершення гри
     */
    checkGameEnd() {
        if (this.score >= this.targetScore) {
            // Перемога - перехід на наступний рівень
            this.levelUp();
        } else {
            // Поразка - кінець гри
            this.gameState = 'gameOver';
            this.soundManager.playSound('gameOver');
            this.interface.showGameOver(this.score, this.level);
        }
    }
    
    /**
     * Пауза гри
     */
    pause() {
        this.isPaused = true;
        this.soundManager.pauseBackgroundMusic();
        console.log('Гра поставлена на паузу');
    }
    
    /**
     * Відновлення гри
     */
    resume() {
        this.isPaused = false;
        this.soundManager.resumeBackgroundMusic();
        console.log('Гра відновлена');
    }
    
    /**
     * Обробка зміни розміру вікна
     */
    handleResize() {
        if (this.renderer) {
            this.renderer.handleResize();
        }
        if (this.interface) {
            this.interface.handleResize();
        }
    }
    
    /**
     * Очищення ресурсів при закритті гри
     */
    destroy() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
        
        if (this.soundManager) {
            this.soundManager.destroy();
        }
        
        if (this.storage) {
            this.saveGame();
        }
        
        console.log('Гра завершена та ресурси очищені');
    }
}
