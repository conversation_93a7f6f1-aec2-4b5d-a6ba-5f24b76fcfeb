/**
 * Система частинок та візуальних ефектів
 * Реалізує процедурну генерацію ефектів для гри
 */

export class ParticleSystem {
    constructor(renderer) {
        this.renderer = renderer;
        this.particles = [];
        this.effects = [];
        this.maxParticles = 500;
        
        // Типи ефектів
        this.effectTypes = {
            explosion: this.createExplosionEffect.bind(this),
            sparkle: this.createSparkleEffect.bind(this),
            smoke: this.createSmokeEffect.bind(this),
            fire: this.createFireEffect.bind(this),
            water: this.createWaterEffect.bind(this),
            energy: this.createEnergyEffect.bind(this),
            reaction: this.createReactionEffect.bind(this)
        };
    }
    
    /**
     * Створення ефекту вибуху
     */
    createExplosionEffect(x, y, color, intensity = 1) {
        const particleCount = Math.floor(15 * intensity);
        
        for (let i = 0; i < particleCount; i++) {
            const angle = (Math.PI * 2 * i) / particleCount;
            const speed = 2 + Math.random() * 4;
            const size = 2 + Math.random() * 4;
            
            this.particles.push(new Particle({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                size: size,
                color: color,
                life: 1.0,
                decay: 0.02 + Math.random() * 0.02,
                type: 'explosion'
            }));
        }
    }
    
    /**
     * Створення ефекту іскор
     */
    createSparkleEffect(x, y, color, count = 8) {
        for (let i = 0; i < count; i++) {
            this.particles.push(new Particle({
                x: x + (Math.random() - 0.5) * 20,
                y: y + (Math.random() - 0.5) * 20,
                vx: (Math.random() - 0.5) * 2,
                vy: (Math.random() - 0.5) * 2,
                size: 1 + Math.random() * 2,
                color: color,
                life: 1.0,
                decay: 0.03,
                type: 'sparkle'
            }));
        }
    }
    
    /**
     * Створення ефекту диму
     */
    createSmokeEffect(x, y, color = '#bdc3c7', duration = 2000) {
        const particleCount = 12;
        
        for (let i = 0; i < particleCount; i++) {
            this.particles.push(new Particle({
                x: x + (Math.random() - 0.5) * 10,
                y: y,
                vx: (Math.random() - 0.5) * 1,
                vy: -1 - Math.random() * 2,
                size: 8 + Math.random() * 12,
                color: color,
                life: 1.0,
                decay: 0.01,
                type: 'smoke'
            }));
        }
    }
    
    /**
     * Створення ефекту вогню
     */
    createFireEffect(x, y, intensity = 1) {
        const colors = ['#e74c3c', '#f39c12', '#e67e22'];
        const particleCount = Math.floor(10 * intensity);
        
        for (let i = 0; i < particleCount; i++) {
            const color = colors[Math.floor(Math.random() * colors.length)];
            
            this.particles.push(new Particle({
                x: x + (Math.random() - 0.5) * 15,
                y: y + Math.random() * 10,
                vx: (Math.random() - 0.5) * 1,
                vy: -2 - Math.random() * 3,
                size: 3 + Math.random() * 6,
                color: color,
                life: 1.0,
                decay: 0.025,
                type: 'fire'
            }));
        }
    }
    
    /**
     * Створення ефекту води
     */
    createWaterEffect(x, y, intensity = 1) {
        const colors = ['#3498db', '#2980b9', '#5dade2'];
        const particleCount = Math.floor(12 * intensity);
        
        for (let i = 0; i < particleCount; i++) {
            const color = colors[Math.floor(Math.random() * colors.length)];
            const angle = Math.random() * Math.PI * 2;
            const speed = 1 + Math.random() * 3;
            
            this.particles.push(new Particle({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                size: 2 + Math.random() * 4,
                color: color,
                life: 1.0,
                decay: 0.02,
                type: 'water'
            }));
        }
    }
    
    /**
     * Створення ефекту енергії
     */
    createEnergyEffect(x, y, intensity = 1) {
        const colors = ['#f39c12', '#e67e22', '#f1c40f'];
        const particleCount = Math.floor(20 * intensity);
        
        for (let i = 0; i < particleCount; i++) {
            const color = colors[Math.floor(Math.random() * colors.length)];
            const angle = Math.random() * Math.PI * 2;
            const speed = 3 + Math.random() * 5;
            
            this.particles.push(new Particle({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                size: 1 + Math.random() * 3,
                color: color,
                life: 1.0,
                decay: 0.04,
                type: 'energy'
            }));
        }
    }
    
    /**
     * Створення ефекту хімічної реакції
     */
    createReactionEffect(x, y, reactants, product) {
        // Ефект злиття реагентів
        const reactantColors = reactants.map(r => r.color);
        
        // Створення кільцевого ефекту
        for (let i = 0; i < 24; i++) {
            const angle = (Math.PI * 2 * i) / 24;
            const radius = 30 + Math.random() * 20;
            
            this.particles.push(new Particle({
                x: x + Math.cos(angle) * radius,
                y: y + Math.sin(angle) * radius,
                vx: -Math.cos(angle) * 2,
                vy: -Math.sin(angle) * 2,
                size: 3 + Math.random() * 3,
                color: reactantColors[i % reactantColors.length],
                life: 1.0,
                decay: 0.015,
                type: 'reaction'
            }));
        }
        
        // Центральний ефект продукту
        this.createExplosionEffect(x, y, product.color, 1.5);
        
        // Додавання світлового ефекту
        this.effects.push(new LightEffect(x, y, product.color, 1000));
    }
    
    /**
     * Оновлення всіх частинок та ефектів
     */
    update(deltaTime) {
        // Оновлення частинок
        this.particles = this.particles.filter(particle => {
            particle.update(deltaTime);
            return particle.isAlive();
        });
        
        // Оновлення ефектів
        this.effects = this.effects.filter(effect => {
            effect.update(deltaTime);
            return effect.isAlive();
        });
        
        // Обмеження кількості частинок для продуктивності
        if (this.particles.length > this.maxParticles) {
            this.particles = this.particles.slice(-this.maxParticles);
        }
    }
    
    /**
     * Рендеринг всіх частинок та ефектів
     */
    render(ctx) {
        // Рендеринг ефектів (під частинками)
        this.effects.forEach(effect => effect.render(ctx));
        
        // Рендеринг частинок
        this.particles.forEach(particle => particle.render(ctx));
    }
    
    /**
     * Створення ефекту за типом
     */
    createEffect(type, x, y, ...args) {
        const effectFunction = this.effectTypes[type];
        if (effectFunction) {
            effectFunction(x, y, ...args);
        } else {
            console.warn(`Невідомий тип ефекту: ${type}`);
        }
    }
    
    /**
     * Очищення всіх ефектів
     */
    clear() {
        this.particles = [];
        this.effects = [];
    }
}

/**
 * Клас окремої частинки
 */
export class Particle {
    constructor(options) {
        this.x = options.x || 0;
        this.y = options.y || 0;
        this.vx = options.vx || 0;
        this.vy = options.vy || 0;
        this.size = options.size || 2;
        this.color = options.color || '#ffffff';
        this.life = options.life || 1.0;
        this.decay = options.decay || 0.02;
        this.type = options.type || 'default';
        
        // Додаткові властивості залежно від типу
        this.gravity = options.gravity || 0.1;
        this.bounce = options.bounce || 0.8;
        this.friction = options.friction || 0.99;
        
        // Анімаційні властивості
        this.rotation = 0;
        this.rotationSpeed = (Math.random() - 0.5) * 0.2;
        this.scale = 1;
        this.alpha = 1;
    }
    
    /**
     * Оновлення частинки
     */
    update(deltaTime) {
        // Оновлення позиції
        this.x += this.vx;
        this.y += this.vy;
        
        // Застосування фізики залежно від типу
        switch (this.type) {
            case 'explosion':
                this.vy += this.gravity;
                this.vx *= this.friction;
                this.vy *= this.friction;
                break;
                
            case 'fire':
                this.vy += this.gravity * 0.5; // Менша гравітація для вогню
                this.vx += (Math.random() - 0.5) * 0.1; // Випадкове коливання
                break;
                
            case 'water':
                this.vy += this.gravity * 1.5; // Більша гравітація для води
                break;
                
            case 'smoke':
                this.vy += this.gravity * 0.2; // Дим піднімається
                this.vx += (Math.random() - 0.5) * 0.05;
                this.size += 0.1; // Дим розширюється
                break;
                
            case 'energy':
                // Енергія рухається хаотично
                this.vx += (Math.random() - 0.5) * 0.3;
                this.vy += (Math.random() - 0.5) * 0.3;
                break;
                
            case 'sparkle':
                this.vy += this.gravity * 0.3;
                this.rotationSpeed *= 1.02; // Прискорення обертання
                break;
        }
        
        // Оновлення анімаційних властивостей
        this.rotation += this.rotationSpeed;
        this.life -= this.decay;
        this.alpha = Math.max(0, this.life);
        
        // Ефект мерехтіння для іскор
        if (this.type === 'sparkle') {
            this.alpha *= 0.5 + 0.5 * Math.sin(Date.now() * 0.01);
        }
        
        // Ефект пульсації для енергії
        if (this.type === 'energy') {
            this.scale = 1 + 0.3 * Math.sin(Date.now() * 0.02);
        }
    }
    
    /**
     * Рендеринг частинки
     */
    render(ctx) {
        if (this.life <= 0) return;
        
        ctx.save();
        
        // Застосування трансформацій
        ctx.translate(this.x, this.y);
        ctx.rotate(this.rotation);
        ctx.scale(this.scale, this.scale);
        ctx.globalAlpha = this.alpha;
        
        // Малювання залежно від типу
        switch (this.type) {
            case 'explosion':
            case 'water':
            case 'energy':
                this.renderCircle(ctx);
                break;
                
            case 'fire':
                this.renderFire(ctx);
                break;
                
            case 'smoke':
                this.renderSmoke(ctx);
                break;
                
            case 'sparkle':
                this.renderSparkle(ctx);
                break;
                
            case 'reaction':
                this.renderReaction(ctx);
                break;
                
            default:
                this.renderCircle(ctx);
        }
        
        ctx.restore();
    }
    
    /**
     * Рендеринг кругової частинки
     */
    renderCircle(ctx) {
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.size, 0, Math.PI * 2);
        ctx.fill();
    }
    
    /**
     * Рендеринг частинки вогню
     */
    renderFire(ctx) {
        // Градієнт для ефекту вогню
        const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, this.size);
        gradient.addColorStop(0, this.color);
        gradient.addColorStop(0.7, tinycolor(this.color).darken(20).toHexString());
        gradient.addColorStop(1, 'transparent');
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(0, 0, this.size, 0, Math.PI * 2);
        ctx.fill();
    }
    
    /**
     * Рендеринг частинки диму
     */
    renderSmoke(ctx) {
        const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, this.size);
        gradient.addColorStop(0, `rgba(189, 195, 199, ${this.alpha * 0.6})`);
        gradient.addColorStop(1, 'transparent');
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(0, 0, this.size, 0, Math.PI * 2);
        ctx.fill();
    }
    
    /**
     * Рендеринг іскри
     */
    renderSparkle(ctx) {
        ctx.fillStyle = this.color;
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 5;
        
        // Малювання зірочки
        ctx.beginPath();
        for (let i = 0; i < 5; i++) {
            const angle = (i * Math.PI * 2) / 5;
            const x = Math.cos(angle) * this.size;
            const y = Math.sin(angle) * this.size;
            
            if (i === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }
        ctx.closePath();
        ctx.fill();
        
        ctx.shadowBlur = 0;
    }
    
    /**
     * Рендеринг частинки реакції
     */
    renderReaction(ctx) {
        // Ефект світіння
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 10;
        
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.size, 0, Math.PI * 2);
        ctx.fill();
        
        // Внутрішнє світло
        const innerColor = tinycolor(this.color).lighten(30).toHexString();
        ctx.fillStyle = innerColor;
        ctx.beginPath();
        ctx.arc(0, 0, this.size * 0.5, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.shadowBlur = 0;
    }
    
    /**
     * Перевірка чи частинка жива
     */
    isAlive() {
        return this.life > 0;
    }
}

/**
 * Клас світлового ефекту
 */
export class LightEffect {
    constructor(x, y, color, duration = 1000) {
        this.x = x;
        this.y = y;
        this.color = color;
        this.duration = duration;
        this.elapsed = 0;
        this.maxRadius = 50;
        this.intensity = 1;
    }
    
    /**
     * Оновлення ефекту
     */
    update(deltaTime) {
        this.elapsed += deltaTime;
        
        // Розрахунок прогресу
        const progress = this.elapsed / this.duration;
        
        // Ефект згасання
        this.intensity = 1 - this.easeOutQuad(progress);
        
        if (progress >= 1) {
            this.intensity = 0;
        }
    }
    
    /**
     * Рендеринг світлового ефекту
     */
    render(ctx) {
        if (this.intensity <= 0) return;
        
        ctx.save();
        
        // Створення радіального градієнту
        const gradient = ctx.createRadialGradient(
            this.x, this.y, 0,
            this.x, this.y, this.maxRadius * this.intensity
        );
        
        const color = tinycolor(this.color);
        gradient.addColorStop(0, color.setAlpha(this.intensity * 0.8).toRgbaString());
        gradient.addColorStop(0.5, color.setAlpha(this.intensity * 0.4).toRgbaString());
        gradient.addColorStop(1, color.setAlpha(0).toRgbaString());
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.maxRadius * this.intensity, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.restore();
    }
    
    /**
     * Функція згладжування
     */
    easeOutQuad(t) {
        return 1 - (1 - t) * (1 - t);
    }
    
    /**
     * Перевірка чи ефект активний
     */
    isAlive() {
        return this.intensity > 0;
    }
}

/**
 * Клас ефекту сліду
 */
export class TrailEffect {
    constructor(startX, startY, endX, endY, color, duration = 500) {
        this.startX = startX;
        this.startY = startY;
        this.endX = endX;
        this.endY = endY;
        this.color = color;
        this.duration = duration;
        this.elapsed = 0;
        this.segments = [];
        
        // Створення сегментів сліду
        const segmentCount = 10;
        for (let i = 0; i < segmentCount; i++) {
            const progress = i / (segmentCount - 1);
            this.segments.push({
                x: this.startX + (this.endX - this.startX) * progress,
                y: this.startY + (this.endY - this.startY) * progress,
                alpha: 1 - progress * 0.5
            });
        }
    }
    
    /**
     * Оновлення ефекту сліду
     */
    update(deltaTime) {
        this.elapsed += deltaTime;
        
        const progress = this.elapsed / this.duration;
        
        // Оновлення альфа-каналу сегментів
        this.segments.forEach((segment, index) => {
            const segmentProgress = index / (this.segments.length - 1);
            segment.alpha = (1 - progress) * (1 - segmentProgress * 0.5);
        });
    }
    
    /**
     * Рендеринг сліду
     */
    render(ctx) {
        ctx.save();
        
        for (let i = 0; i < this.segments.length - 1; i++) {
            const segment = this.segments[i];
            const nextSegment = this.segments[i + 1];
            
            if (segment.alpha <= 0) continue;
            
            ctx.globalAlpha = segment.alpha;
            ctx.strokeStyle = this.color;
            ctx.lineWidth = 3 * segment.alpha;
            ctx.lineCap = 'round';
            
            ctx.beginPath();
            ctx.moveTo(segment.x, segment.y);
            ctx.lineTo(nextSegment.x, nextSegment.y);
            ctx.stroke();
        }
        
        ctx.restore();
    }
    
    /**
     * Перевірка чи ефект активний
     */
    isAlive() {
        return this.elapsed < this.duration;
    }
}
