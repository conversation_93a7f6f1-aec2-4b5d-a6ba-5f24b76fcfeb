/**
 * К<PERSON><PERSON><PERSON> SFXGen - генератор звукових ефектів
 * Створює процедурні звукові ефекти для різних ігрових подій
 */

export class SFXGenerator {
    constructor(audioContext) {
        this.audioContext = audioContext;
        
        // Параметри для різних типів звуків
        this.soundProfiles = {
            match: {
                type: 'sine',
                startFreq: 440,
                endFreq: 880,
                duration: 0.3,
                volume: 0.3,
                envelope: 'bell'
            },
            
            invalidMove: {
                type: 'sawtooth',
                startFreq: 200,
                endFreq: 150,
                duration: 0.2,
                volume: 0.2,
                envelope: 'decay'
            },
            
            levelUp: {
                type: 'triangle',
                notes: [261.63, 329.63, 392.00, 523.25], // C, E, G, C
                duration: 0.5,
                volume: 0.4,
                envelope: 'sustain'
            },
            
            gameOver: {
                type: 'square',
                startFreq: 440,
                endFreq: 220,
                duration: 1.0,
                volume: 0.3,
                envelope: 'fade'
            },
            
            reaction: {
                type: 'sine',
                startFreq: 300,
                modulation: true,
                duration: 0.8,
                volume: 0.4,
                envelope: 'complex'
            },
            
            cascade: {
                type: 'sawtooth',
                startFreq: 220,
                endFreq: 440,
                duration: 0.3,
                volume: 0.3,
                envelope: 'rise'
            },
            
            gravity: {
                type: 'triangle',
                startFreq: 150,
                endFreq: 300,
                duration: 0.4,
                volume: 0.3,
                envelope: 'wave'
            },
            
            button: {
                type: 'sine',
                startFreq: 800,
                duration: 0.1,
                volume: 0.2,
                envelope: 'click'
            }
        };
    }
    
    /**
     * Генерація звукового ефекту
     */
    generateSFX(type, variant = null) {
        const profile = this.soundProfiles[type];
        if (!profile) {
            console.warn(`Невідомий тип звукового ефекту: ${type}`);
            return;
        }
        
        const startTime = this.audioContext.currentTime;
        
        switch (profile.envelope) {
            case 'bell':
                this.createBellSound(profile, startTime);
                break;
            case 'decay':
                this.createDecaySound(profile, startTime);
                break;
            case 'sustain':
                this.createSustainSound(profile, startTime);
                break;
            case 'fade':
                this.createFadeSound(profile, startTime);
                break;
            case 'complex':
                this.createComplexSound(profile, startTime);
                break;
            case 'rise':
                this.createRiseSound(profile, startTime);
                break;
            case 'wave':
                this.createWaveSound(profile, startTime);
                break;
            case 'click':
                this.createClickSound(profile, startTime);
                break;
            default:
                this.createBasicSound(profile, startTime);
        }
    }
    
    /**
     * Створення дзвінкого звуку
     */
    createBellSound(profile, startTime) {
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        
        osc.type = profile.type;
        osc.frequency.setValueAtTime(profile.startFreq, startTime);
        osc.frequency.exponentialRampToValueAtTime(profile.endFreq, startTime + profile.duration);
        
        osc.connect(gain);
        gain.connect(this.audioContext.destination);
        
        // Bell envelope
        gain.gain.setValueAtTime(0, startTime);
        gain.gain.linearRampToValueAtTime(profile.volume, startTime + 0.01);
        gain.gain.exponentialRampToValueAtTime(profile.volume * 0.3, startTime + profile.duration * 0.3);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + profile.duration);
        
        osc.start(startTime);
        osc.stop(startTime + profile.duration);
    }
    
    /**
     * Створення звуку з загасанням
     */
    createDecaySound(profile, startTime) {
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        
        osc.type = profile.type;
        osc.frequency.setValueAtTime(profile.startFreq, startTime);
        osc.frequency.linearRampToValueAtTime(profile.endFreq, startTime + profile.duration);
        
        osc.connect(gain);
        gain.connect(this.audioContext.destination);
        
        gain.gain.setValueAtTime(profile.volume, startTime);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + profile.duration);
        
        osc.start(startTime);
        osc.stop(startTime + profile.duration);
    }
    
    /**
     * Створення звуку з підтримкою
     */
    createSustainSound(profile, startTime) {
        if (profile.notes) {
            // Арпеджіо для levelUp
            profile.notes.forEach((freq, index) => {
                const osc = this.audioContext.createOscillator();
                const gain = this.audioContext.createGain();
                
                osc.type = profile.type;
                osc.frequency.value = freq;
                
                osc.connect(gain);
                gain.connect(this.audioContext.destination);
                
                const noteStart = startTime + index * 0.1;
                gain.gain.setValueAtTime(0, noteStart);
                gain.gain.linearRampToValueAtTime(profile.volume, noteStart + 0.01);
                gain.gain.exponentialRampToValueAtTime(0.01, noteStart + profile.duration);
                
                osc.start(noteStart);
                osc.stop(noteStart + profile.duration);
            });
        }
    }
    
    /**
     * Створення звуку з поступовим згасанням
     */
    createFadeSound(profile, startTime) {
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        
        osc.type = profile.type;
        osc.frequency.setValueAtTime(profile.startFreq, startTime);
        osc.frequency.exponentialRampToValueAtTime(profile.endFreq, startTime + profile.duration);
        
        osc.connect(gain);
        gain.connect(this.audioContext.destination);
        
        gain.gain.setValueAtTime(profile.volume, startTime);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + profile.duration);
        
        osc.start(startTime);
        osc.stop(startTime + profile.duration);
    }
    
    /**
     * Створення складного звуку з модуляцією
     */
    createComplexSound(profile, startTime) {
        const carrier = this.audioContext.createOscillator();
        const modulator = this.audioContext.createOscillator();
        const modulatorGain = this.audioContext.createGain();
        const gain = this.audioContext.createGain();
        
        // Налаштування модуляції
        modulator.type = 'sine';
        modulator.frequency.value = 5; // 5 Hz модуляція
        modulatorGain.gain.value = 50; // Глибина модуляції
        
        modulator.connect(modulatorGain);
        modulatorGain.connect(carrier.frequency);
        
        carrier.type = profile.type;
        carrier.frequency.value = profile.startFreq;
        
        carrier.connect(gain);
        gain.connect(this.audioContext.destination);
        
        gain.gain.setValueAtTime(0, startTime);
        gain.gain.linearRampToValueAtTime(profile.volume, startTime + 0.1);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + profile.duration);
        
        carrier.start(startTime);
        modulator.start(startTime);
        carrier.stop(startTime + profile.duration);
        modulator.stop(startTime + profile.duration);
    }
    
    /**
     * Створення наростаючого звуку
     */
    createRiseSound(profile, startTime) {
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        const filter = this.audioContext.createBiquadFilter();
        
        osc.type = profile.type;
        osc.frequency.setValueAtTime(profile.startFreq, startTime);
        osc.frequency.exponentialRampToValueAtTime(profile.endFreq, startTime + profile.duration);
        
        filter.type = 'lowpass';
        filter.frequency.setValueAtTime(1000, startTime);
        filter.frequency.exponentialRampToValueAtTime(2000, startTime + profile.duration);
        
        osc.connect(filter);
        filter.connect(gain);
        gain.connect(this.audioContext.destination);
        
        gain.gain.setValueAtTime(0, startTime);
        gain.gain.linearRampToValueAtTime(profile.volume, startTime + profile.duration * 0.8);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + profile.duration);
        
        osc.start(startTime);
        osc.stop(startTime + profile.duration);
    }
    
    /**
     * Створення хвилеподібного звуку
     */
    createWaveSound(profile, startTime) {
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        
        osc.type = profile.type;
        
        // Хвилеподібна зміна частоти
        const freqRange = profile.endFreq - profile.startFreq;
        const steps = 20;
        
        for (let i = 0; i <= steps; i++) {
            const time = startTime + (profile.duration * i) / steps;
            const progress = i / steps;
            const waveValue = Math.sin(progress * Math.PI * 2);
            const frequency = profile.startFreq + (freqRange * 0.5) + (freqRange * 0.5 * waveValue);
            
            osc.frequency.setValueAtTime(frequency, time);
        }
        
        osc.connect(gain);
        gain.connect(this.audioContext.destination);
        
        gain.gain.setValueAtTime(0, startTime);
        gain.gain.linearRampToValueAtTime(profile.volume, startTime + 0.05);
        gain.gain.linearRampToValueAtTime(profile.volume * 0.7, startTime + profile.duration * 0.5);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + profile.duration);
        
        osc.start(startTime);
        osc.stop(startTime + profile.duration);
    }
    
    /**
     * Створення звуку кліку
     */
    createClickSound(profile, startTime) {
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        
        osc.type = profile.type;
        osc.frequency.value = profile.startFreq;
        
        osc.connect(gain);
        gain.connect(this.audioContext.destination);
        
        gain.gain.setValueAtTime(profile.volume, startTime);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + profile.duration);
        
        osc.start(startTime);
        osc.stop(startTime + profile.duration);
    }
    
    /**
     * Створення базового звуку
     */
    createBasicSound(profile, startTime) {
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        
        osc.type = profile.type;
        osc.frequency.value = profile.startFreq;
        
        osc.connect(gain);
        gain.connect(this.audioContext.destination);
        
        gain.gain.setValueAtTime(0, startTime);
        gain.gain.linearRampToValueAtTime(profile.volume, startTime + 0.01);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + profile.duration);
        
        osc.start(startTime);
        osc.stop(startTime + profile.duration);
    }
    
    /**
     * Створення звуку на основі елемента
     */
    createElementSound(element, action = 'match') {
        const elementSounds = {
            fire: { freq: 400, type: 'sawtooth', mod: 'crackle' },
            water: { freq: 300, type: 'sine', mod: 'flow' },
            earth: { freq: 200, type: 'square', mod: 'rumble' },
            air: { freq: 600, type: 'triangle', mod: 'whistle' },
            energy: { freq: 500, type: 'sine', mod: 'zap' },
            crystal: { freq: 800, type: 'triangle', mod: 'chime' }
        };
        
        const elementName = Object.keys(this.getElementMap()).find(key => 
            this.getElementMap()[key].id === element.id
        );
        
        const soundData = elementSounds[elementName] || elementSounds.crystal;
        
        this.createElementSpecificSound(soundData, action);
    }
    
    /**
     * Створення звуку специфічного для елемента
     */
    createElementSpecificSound(soundData, action) {
        const startTime = this.audioContext.currentTime;
        
        switch (soundData.mod) {
            case 'crackle':
                this.createCrackleSound(soundData, startTime);
                break;
            case 'flow':
                this.createFlowSound(soundData, startTime);
                break;
            case 'rumble':
                this.createRumbleSound(soundData, startTime);
                break;
            case 'whistle':
                this.createWhistleSound(soundData, startTime);
                break;
            case 'zap':
                this.createZapSound(soundData, startTime);
                break;
            case 'chime':
                this.createChimeSound(soundData, startTime);
                break;
        }
    }
    
    /**
     * Звук тріскання (вогонь)
     */
    createCrackleSound(soundData, startTime) {
        for (let i = 0; i < 5; i++) {
            const osc = this.audioContext.createOscillator();
            const gain = this.audioContext.createGain();
            
            osc.type = 'sawtooth';
            osc.frequency.value = soundData.freq + Math.random() * 100;
            
            osc.connect(gain);
            gain.connect(this.audioContext.destination);
            
            const noteStart = startTime + i * 0.05;
            gain.gain.setValueAtTime(0.1, noteStart);
            gain.gain.exponentialRampToValueAtTime(0.01, noteStart + 0.1);
            
            osc.start(noteStart);
            osc.stop(noteStart + 0.1);
        }
    }
    
    /**
     * Звук течії (вода)
     */
    createFlowSound(soundData, startTime) {
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        const filter = this.audioContext.createBiquadFilter();
        
        osc.type = 'sine';
        osc.frequency.value = soundData.freq;
        
        filter.type = 'lowpass';
        filter.frequency.setValueAtTime(1000, startTime);
        filter.frequency.linearRampToValueAtTime(500, startTime + 0.3);
        
        osc.connect(filter);
        filter.connect(gain);
        gain.connect(this.audioContext.destination);
        
        gain.gain.setValueAtTime(0, startTime);
        gain.gain.linearRampToValueAtTime(0.2, startTime + 0.1);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + 0.3);
        
        osc.start(startTime);
        osc.stop(startTime + 0.3);
    }
    
    /**
     * Звук гуркоту (земля)
     */
    createRumbleSound(soundData, startTime) {
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        
        osc.type = 'square';
        osc.frequency.setValueAtTime(soundData.freq, startTime);
        osc.frequency.linearRampToValueAtTime(soundData.freq * 0.8, startTime + 0.2);
        
        osc.connect(gain);
        gain.connect(this.audioContext.destination);
        
        gain.gain.setValueAtTime(0.2, startTime);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + 0.2);
        
        osc.start(startTime);
        osc.stop(startTime + 0.2);
    }
    
    /**
     * Звук свисту (повітря)
     */
    createWhistleSound(soundData, startTime) {
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        const filter = this.audioContext.createBiquadFilter();
        
        osc.type = 'triangle';
        osc.frequency.setValueAtTime(soundData.freq, startTime);
        osc.frequency.linearRampToValueAtTime(soundData.freq * 1.5, startTime + 0.2);
        
        filter.type = 'highpass';
        filter.frequency.value = 400;
        
        osc.connect(filter);
        filter.connect(gain);
        gain.connect(this.audioContext.destination);
        
        gain.gain.setValueAtTime(0, startTime);
        gain.gain.linearRampToValueAtTime(0.15, startTime + 0.05);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + 0.2);
        
        osc.start(startTime);
        osc.stop(startTime + 0.2);
    }
    
    /**
     * Звук розряду (енергія)
     */
    createZapSound(soundData, startTime) {
        // Створення білого шуму для ефекту розряду
        const bufferSize = this.audioContext.sampleRate * 0.1;
        const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < bufferSize; i++) {
            data[i] = Math.random() * 2 - 1;
        }
        
        const source = this.audioContext.createBufferSource();
        const gain = this.audioContext.createGain();
        const filter = this.audioContext.createBiquadFilter();
        
        source.buffer = buffer;
        filter.type = 'bandpass';
        filter.frequency.value = soundData.freq;
        filter.Q.value = 10;
        
        source.connect(filter);
        filter.connect(gain);
        gain.connect(this.audioContext.destination);
        
        gain.gain.setValueAtTime(0.3, startTime);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + 0.1);
        
        source.start(startTime);
    }
    
    /**
     * Звук дзвону (кристал)
     */
    createChimeSound(soundData, startTime) {
        // Створення гармонік для дзвінкого звуку
        const harmonics = [1, 2, 3, 4, 5];
        
        harmonics.forEach((harmonic, index) => {
            const osc = this.audioContext.createOscillator();
            const gain = this.audioContext.createGain();
            
            osc.type = 'sine';
            osc.frequency.value = soundData.freq * harmonic;
            
            osc.connect(gain);
            gain.connect(this.audioContext.destination);
            
            const volume = 0.1 / harmonic; // Зменшення гучності для вищих гармонік
            gain.gain.setValueAtTime(0, startTime);
            gain.gain.linearRampToValueAtTime(volume, startTime + 0.01);
            gain.gain.exponentialRampToValueAtTime(0.01, startTime + 0.5);
            
            osc.start(startTime);
            osc.stop(startTime + 0.5);
        });
    }
    
    /**
     * Отримання карти елементів
     */
    getElementMap() {
        return {
            fire: { id: 0 },
            water: { id: 1 },
            earth: { id: 2 },
            air: { id: 3 },
            energy: { id: 4 },
            crystal: { id: 5 }
        };
    }
}
