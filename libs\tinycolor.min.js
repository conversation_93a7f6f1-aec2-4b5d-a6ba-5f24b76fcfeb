/**
 * TinyColor2 v1.6.0 - Заглушка для демонстрації
 * Мінімальна реалізація для маніпуляції кольорами
 */
(function(global) {
    'use strict';
    
    function tinycolor(color) {
        return new TinyColor(color);
    }
    
    function TinyColor(color) {
        this._originalInput = color;
        this._r = 0;
        this._g = 0;
        this._b = 0;
        this._a = 1;
        
        if (color) {
            this._parseInput(color);
        }
    }
    
    TinyColor.prototype = {
        _parseInput: function(color) {
            if (typeof color === 'string') {
                if (color.startsWith('#')) {
                    this._parseHex(color);
                } else if (color.startsWith('rgb')) {
                    this._parseRgb(color);
                } else {
                    this._parseNamed(color);
                }
            } else if (typeof color === 'object') {
                if (color.r !== undefined) {
                    this._r = Math.max(0, Math.min(255, color.r));
                    this._g = Math.max(0, Math.min(255, color.g));
                    this._b = Math.max(0, Math.min(255, color.b));
                    this._a = color.a !== undefined ? color.a : 1;
                }
            }
        },
        
        _parseHex: function(hex) {
            hex = hex.replace('#', '');
            if (hex.length === 3) {
                hex = hex.split('').map(c => c + c).join('');
            }
            const num = parseInt(hex, 16);
            this._r = (num >> 16) & 255;
            this._g = (num >> 8) & 255;
            this._b = num & 255;
        },
        
        _parseRgb: function(rgb) {
            const match = rgb.match(/rgba?\(([^)]+)\)/);
            if (match) {
                const values = match[1].split(',').map(v => parseFloat(v.trim()));
                this._r = values[0] || 0;
                this._g = values[1] || 0;
                this._b = values[2] || 0;
                this._a = values[3] !== undefined ? values[3] : 1;
            }
        },
        
        _parseNamed: function(name) {
            const colors = {
                red: '#ff0000', green: '#008000', blue: '#0000ff',
                white: '#ffffff', black: '#000000', yellow: '#ffff00',
                cyan: '#00ffff', magenta: '#ff00ff', orange: '#ffa500',
                purple: '#800080', pink: '#ffc0cb', brown: '#a52a2a'
            };
            if (colors[name.toLowerCase()]) {
                this._parseHex(colors[name.toLowerCase()]);
            }
        },
        
        toHexString: function() {
            const hex = ((1 << 24) + (this._r << 16) + (this._g << 8) + this._b).toString(16).slice(1);
            return '#' + hex;
        },
        
        toRgbString: function() {
            return `rgb(${Math.round(this._r)}, ${Math.round(this._g)}, ${Math.round(this._b)})`;
        },
        
        toRgbaString: function() {
            return `rgba(${Math.round(this._r)}, ${Math.round(this._g)}, ${Math.round(this._b)}, ${this._a})`;
        },
        
        toHsl: function() {
            const r = this._r / 255;
            const g = this._g / 255;
            const b = this._b / 255;
            
            const max = Math.max(r, g, b);
            const min = Math.min(r, g, b);
            const diff = max - min;
            const sum = max + min;
            const l = sum / 2;
            
            let h, s;
            
            if (diff === 0) {
                h = s = 0;
            } else {
                s = l > 0.5 ? diff / (2 - sum) : diff / sum;
                
                switch (max) {
                    case r: h = (g - b) / diff + (g < b ? 6 : 0); break;
                    case g: h = (b - r) / diff + 2; break;
                    case b: h = (r - g) / diff + 4; break;
                }
                h /= 6;
            }
            
            return { h: h * 360, s: s * 100, l: l * 100 };
        },
        
        lighten: function(amount = 10) {
            const hsl = this.toHsl();
            hsl.l = Math.min(100, hsl.l + amount);
            return tinycolor(hsl);
        },
        
        darken: function(amount = 10) {
            const hsl = this.toHsl();
            hsl.l = Math.max(0, hsl.l - amount);
            return tinycolor(hsl);
        },
        
        saturate: function(amount = 10) {
            const hsl = this.toHsl();
            hsl.s = Math.min(100, hsl.s + amount);
            return tinycolor(hsl);
        },
        
        desaturate: function(amount = 10) {
            const hsl = this.toHsl();
            hsl.s = Math.max(0, hsl.s - amount);
            return tinycolor(hsl);
        },
        
        spin: function(amount) {
            const hsl = this.toHsl();
            hsl.h = (hsl.h + amount) % 360;
            if (hsl.h < 0) hsl.h += 360;
            return tinycolor(hsl);
        },
        
        complement: function() {
            return this.spin(180);
        },
        
        analogous: function(results = 6, slices = 30) {
            const colors = [this];
            const part = 360 / slices;
            
            for (let i = 1; i < results; i++) {
                colors.push(this.spin(part * i));
            }
            
            return colors;
        },
        
        monochromatic: function(results = 6) {
            const colors = [];
            const hsl = this.toHsl();
            
            for (let i = 0; i < results; i++) {
                const newHsl = { ...hsl };
                newHsl.l = (hsl.l + (i * 10)) % 100;
                colors.push(tinycolor(newHsl));
            }
            
            return colors;
        },
        
        isValid: function() {
            return this._r >= 0 && this._r <= 255 &&
                   this._g >= 0 && this._g <= 255 &&
                   this._b >= 0 && this._b <= 255 &&
                   this._a >= 0 && this._a <= 1;
        },
        
        clone: function() {
            return tinycolor(this._originalInput);
        }
    };
    
    // Статичні методи
    tinycolor.random = function() {
        return tinycolor({
            r: Math.floor(Math.random() * 256),
            g: Math.floor(Math.random() * 256),
            b: Math.floor(Math.random() * 256)
        });
    };
    
    tinycolor.mix = function(color1, color2, amount = 50) {
        const c1 = tinycolor(color1);
        const c2 = tinycolor(color2);
        const p = amount / 100;
        
        return tinycolor({
            r: Math.round(c1._r * (1 - p) + c2._r * p),
            g: Math.round(c1._g * (1 - p) + c2._g * p),
            b: Math.round(c1._b * (1 - p) + c2._b * p),
            a: c1._a * (1 - p) + c2._a * p
        });
    };
    
    // Експорт
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = tinycolor;
    } else if (typeof define === 'function' && define.amd) {
        define([], function() { return tinycolor; });
    } else {
        global.tinycolor = tinycolor;
    }
    
})(typeof window !== 'undefined' ? window : this);
