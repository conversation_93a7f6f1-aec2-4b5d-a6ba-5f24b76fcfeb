/**
 * Клас Grid - управління ігровою сіткою та елементами
 * Реалізує унікальні механіки хімії елементів та гравітації
 */

export class Grid {
    constructor(size = 8, elementTypes = 6) {
        this.size = size;
        this.elementTypes = elementTypes;
        this.cells = [];
        this.animations = [];
        this.gravityDirection = 'down'; // down, up, left, right
        this.gravityRotationTimer = 0;
        this.gravityRotationInterval = 45000; // 45 секунд
        
        // Типи хімічних елементів з їх властивостями
        this.elements = {
            fire: { 
                id: 0, 
                color: '#e74c3c', 
                symbol: '🔥', 
                reactions: { water: 'steam', earth: 'lava' }
            },
            water: { 
                id: 1, 
                color: '#3498db', 
                symbol: '💧', 
                reactions: { fire: 'steam', earth: 'mud' }
            },
            earth: { 
                id: 2, 
                color: '#8b4513', 
                symbol: '🌍', 
                reactions: { fire: 'lava', water: 'mud' }
            },
            air: { 
                id: 3, 
                color: '#ecf0f1', 
                symbol: '💨', 
                reactions: { fire: 'energy', water: 'storm' }
            },
            energy: { 
                id: 4, 
                color: '#f39c12', 
                symbol: '⚡', 
                reactions: { water: 'storm', earth: 'crystal' }
            },
            crystal: { 
                id: 5, 
                color: '#9b59b6', 
                symbol: '💎', 
                reactions: { fire: 'energy', air: 'light' }
            },
            steam: { 
                id: 6, 
                color: '#bdc3c7', 
                symbol: '☁️', 
                reactions: { air: 'storm', crystal: 'mist' }
            },
            lava: { 
                id: 7, 
                color: '#e67e22', 
                symbol: '🌋', 
                reactions: { water: 'steam', air: 'ash' }
            }
        };
        
        this.elementArray = Object.values(this.elements);
    }
    
    /**
     * Ініціалізація сітки
     */
    async initialize() {
        console.log('Ініціалізація ігрової сітки...');
        
        // Створення двовимірного масиву клітинок
        this.cells = Array(this.size).fill(null).map(() => 
            Array(this.size).fill(null).map(() => ({
                element: null,
                isAnimating: false,
                animationType: null,
                animationProgress: 0,
                isSelected: false,
                isHighlighted: false
            }))
        );
        
        // Запуск таймера зміни гравітації
        this.startGravityRotation();
        
        console.log('Ігрова сітка ініціалізована');
    }
    
    /**
     * Генерація нової сітки з елементами
     */
    async generateNewGrid() {
        console.log('Генерація нової ігрової сітки...');
        
        // Заповнення сітки випадковими елементами
        for (let x = 0; x < this.size; x++) {
            for (let y = 0; y < this.size; y++) {
                this.cells[x][y].element = this.getRandomElement();
            }
        }
        
        // Перевірка та усунення початкових збігів
        await this.removeInitialMatches();
        
        // Заповнення порожніх клітинок
        await this.fillEmptyCells();
        
        console.log('Нова сітка згенерована');
    }
    
    /**
     * Отримання випадкового елемента
     */
    getRandomElement() {
        const availableElements = this.elementArray.slice(0, this.elementTypes);
        return availableElements[Math.floor(Math.random() * availableElements.length)];
    }
    
    /**
     * Видалення початкових збігів при генерації
     */
    async removeInitialMatches() {
        let hasMatches = true;
        let attempts = 0;
        const maxAttempts = 100;
        
        while (hasMatches && attempts < maxAttempts) {
            hasMatches = false;
            attempts++;
            
            for (let x = 0; x < this.size; x++) {
                for (let y = 0; y < this.size; y++) {
                    if (this.hasMatchAt(x, y)) {
                        this.cells[x][y].element = this.getRandomElement();
                        hasMatches = true;
                    }
                }
            }
        }
    }
    
    /**
     * Перевірка наявності збігу в позиції
     */
    hasMatchAt(x, y) {
        const element = this.cells[x][y].element;
        if (!element) return false;
        
        // Перевірка горизонтального збігу
        let horizontalCount = 1;
        
        // Ліворуч
        for (let i = x - 1; i >= 0; i--) {
            if (this.cells[i][y].element && this.cells[i][y].element.id === element.id) {
                horizontalCount++;
            } else {
                break;
            }
        }
        
        // Праворуч
        for (let i = x + 1; i < this.size; i++) {
            if (this.cells[i][y].element && this.cells[i][y].element.id === element.id) {
                horizontalCount++;
            } else {
                break;
            }
        }
        
        if (horizontalCount >= 3) return true;
        
        // Перевірка вертикального збігу
        let verticalCount = 1;
        
        // Вгору
        for (let i = y - 1; i >= 0; i--) {
            if (this.cells[x][i].element && this.cells[x][i].element.id === element.id) {
                verticalCount++;
            } else {
                break;
            }
        }
        
        // Вниз
        for (let i = y + 1; i < this.size; i++) {
            if (this.cells[x][i].element && this.cells[x][i].element.id === element.id) {
                verticalCount++;
            } else {
                break;
            }
        }
        
        return verticalCount >= 3;
    }
    
    /**
     * Виконання ходу гравця
     */
    async makeMove(fromX, fromY, toX, toY) {
        console.log(`Хід: (${fromX},${fromY}) -> (${toX},${toY})`);
        
        // Перевірка валідності координат
        if (!this.isValidPosition(fromX, fromY) || !this.isValidPosition(toX, toY)) {
            return { success: false, reason: 'invalid_position' };
        }
        
        // Перевірка що клітинки сусідні
        if (!this.areAdjacent(fromX, fromY, toX, toY)) {
            return { success: false, reason: 'not_adjacent' };
        }
        
        // Збереження початкових елементів
        const fromElement = this.cells[fromX][fromY].element;
        const toElement = this.cells[toX][toY].element;
        
        // Виконання обміну
        this.cells[fromX][fromY].element = toElement;
        this.cells[toX][toY].element = fromElement;
        
        // Перевірка на збіги після обміну
        const matches = this.findAllMatches();
        
        if (matches.length === 0) {
            // Якщо збігів немає, повертаємо елементи назад
            this.cells[fromX][fromY].element = fromElement;
            this.cells[toX][toY].element = toElement;
            return { success: false, reason: 'no_matches' };
        }
        
        // Обробка збігів та хімічних реакцій
        const result = await this.processMatches(matches);
        
        // Застосування гравітації
        await this.applyGravity();
        
        // Заповнення порожніх клітинок
        await this.fillEmptyCells();
        
        // Перевірка на каскадні збіги
        await this.processCascadeMatches();
        
        return {
            success: true,
            matches: matches,
            reactions: result.reactions,
            cascades: result.cascades,
            totalScore: result.totalScore
        };
    }
    
    /**
     * Пошук всіх збігів на сітці
     */
    findAllMatches() {
        const matches = [];
        const processed = Array(this.size).fill(null).map(() => Array(this.size).fill(false));
        
        for (let x = 0; x < this.size; x++) {
            for (let y = 0; y < this.size; y++) {
                if (!processed[x][y] && this.cells[x][y].element) {
                    const match = this.findMatchGroup(x, y, processed);
                    if (match.length >= 3) {
                        matches.push(match);
                    }
                }
            }
        }
        
        return matches;
    }
    
    /**
     * Пошук групи збігів від заданої позиції
     */
    findMatchGroup(startX, startY, processed) {
        const element = this.cells[startX][startY].element;
        const group = [];
        const stack = [{ x: startX, y: startY }];
        
        while (stack.length > 0) {
            const { x, y } = stack.pop();
            
            if (processed[x][y] || !this.cells[x][y].element || 
                this.cells[x][y].element.id !== element.id) {
                continue;
            }
            
            processed[x][y] = true;
            group.push({ x, y, element });
            
            // Перевірка сусідніх клітинок
            const neighbors = [
                { x: x - 1, y }, { x: x + 1, y },
                { x, y: y - 1 }, { x, y: y + 1 }
            ];
            
            for (const neighbor of neighbors) {
                if (this.isValidPosition(neighbor.x, neighbor.y) && 
                    !processed[neighbor.x][neighbor.y]) {
                    stack.push(neighbor);
                }
            }
        }
        
        return group;
    }
    
    /**
     * Обробка збігів та хімічних реакцій
     */
    async processMatches(matches) {
        let totalScore = 0;
        const reactions = [];
        
        for (const match of matches) {
            // Видалення елементів збігу
            for (const cell of match) {
                this.cells[cell.x][cell.y].element = null;
                this.cells[cell.x][cell.y].isAnimating = true;
                this.cells[cell.x][cell.y].animationType = 'disappear';
            }
            
            // Перевірка на хімічні реакції
            const reaction = this.checkChemicalReactions(match);
            if (reaction) {
                reactions.push(reaction);
                await this.applyChemicalReaction(reaction);
            }
            
            // Обчислення очок за збіг
            totalScore += this.calculateMatchScore(match);
        }
        
        return { reactions, totalScore, cascades: 0 };
    }
    
    /**
     * Перевірка хімічних реакцій
     */
    checkChemicalReactions(match) {
        if (match.length < 4) return null;
        
        const element = match[0].element;
        const centerPos = match[Math.floor(match.length / 2)];
        
        // Пошук сусідніх елементів для реакції
        const neighbors = this.getNeighbors(centerPos.x, centerPos.y);
        
        for (const neighbor of neighbors) {
            const neighborElement = this.cells[neighbor.x][neighbor.y].element;
            if (neighborElement && element.reactions[neighborElement.symbol]) {
                const productName = element.reactions[neighborElement.symbol];
                const product = Object.values(this.elements).find(e => 
                    e.symbol === productName || Object.keys(this.elements)[e.id] === productName
                );
                
                if (product) {
                    return {
                        reactants: [element, neighborElement],
                        product: product,
                        position: centerPos
                    };
                }
            }
        }
        
        return null;
    }
    
    /**
     * Застосування хімічної реакції
     */
    async applyChemicalReaction(reaction) {
        const { position, product } = reaction;
        
        // Створення нового елемента в центрі реакції
        this.cells[position.x][position.y].element = product;
        this.cells[position.x][position.y].isAnimating = true;
        this.cells[position.x][position.y].animationType = 'reaction';
        
        console.log(`Хімічна реакція: ${reaction.reactants.map(r => r.symbol).join(' + ')} -> ${product.symbol}`);
    }
    
    /**
     * Застосування гравітації
     */
    async applyGravity() {
        let moved = true;
        
        while (moved) {
            moved = false;
            
            switch (this.gravityDirection) {
                case 'down':
                    moved = this.applyGravityDown();
                    break;
                case 'up':
                    moved = this.applyGravityUp();
                    break;
                case 'left':
                    moved = this.applyGravityLeft();
                    break;
                case 'right':
                    moved = this.applyGravityRight();
                    break;
            }
            
            if (moved) {
                await this.wait(100); // Пауза для анімації
            }
        }
    }
    
    /**
     * Застосування гравітації вниз
     */
    applyGravityDown() {
        let moved = false;
        
        for (let x = 0; x < this.size; x++) {
            for (let y = this.size - 2; y >= 0; y--) {
                if (this.cells[x][y].element && !this.cells[x][y + 1].element) {
                    // Знаходження найнижчої порожньої позиції
                    let targetY = y + 1;
                    while (targetY < this.size - 1 && !this.cells[x][targetY + 1].element) {
                        targetY++;
                    }
                    
                    // Переміщення елемента
                    this.cells[x][targetY].element = this.cells[x][y].element;
                    this.cells[x][y].element = null;
                    
                    // Анімація падіння
                    this.cells[x][targetY].isAnimating = true;
                    this.cells[x][targetY].animationType = 'fall';
                    
                    moved = true;
                }
            }
        }
        
        return moved;
    }
    
    /**
     * Застосування гравітації вгору
     */
    applyGravityUp() {
        let moved = false;
        
        for (let x = 0; x < this.size; x++) {
            for (let y = 1; y < this.size; y++) {
                if (this.cells[x][y].element && !this.cells[x][y - 1].element) {
                    let targetY = y - 1;
                    while (targetY > 0 && !this.cells[x][targetY - 1].element) {
                        targetY--;
                    }
                    
                    this.cells[x][targetY].element = this.cells[x][y].element;
                    this.cells[x][y].element = null;
                    
                    this.cells[x][targetY].isAnimating = true;
                    this.cells[x][targetY].animationType = 'rise';
                    
                    moved = true;
                }
            }
        }
        
        return moved;
    }
    
    /**
     * Застосування гравітації ліворуч
     */
    applyGravityLeft() {
        let moved = false;
        
        for (let y = 0; y < this.size; y++) {
            for (let x = 1; x < this.size; x++) {
                if (this.cells[x][y].element && !this.cells[x - 1][y].element) {
                    let targetX = x - 1;
                    while (targetX > 0 && !this.cells[targetX - 1][y].element) {
                        targetX--;
                    }
                    
                    this.cells[targetX][y].element = this.cells[x][y].element;
                    this.cells[x][y].element = null;
                    
                    this.cells[targetX][y].isAnimating = true;
                    this.cells[targetX][y].animationType = 'slideLeft';
                    
                    moved = true;
                }
            }
        }
        
        return moved;
    }
    
    /**
     * Застосування гравітації праворуч
     */
    applyGravityRight() {
        let moved = false;
        
        for (let y = 0; y < this.size; y++) {
            for (let x = this.size - 2; x >= 0; x--) {
                if (this.cells[x][y].element && !this.cells[x + 1][y].element) {
                    let targetX = x + 1;
                    while (targetX < this.size - 1 && !this.cells[targetX + 1][y].element) {
                        targetX++;
                    }
                    
                    this.cells[targetX][y].element = this.cells[x][y].element;
                    this.cells[x][y].element = null;
                    
                    this.cells[targetX][y].isAnimating = true;
                    this.cells[targetX][y].animationType = 'slideRight';
                    
                    moved = true;
                }
            }
        }
        
        return moved;
    }
    
    /**
     * Заповнення порожніх клітинок новими елементами
     */
    async fillEmptyCells() {
        for (let x = 0; x < this.size; x++) {
            for (let y = 0; y < this.size; y++) {
                if (!this.cells[x][y].element) {
                    this.cells[x][y].element = this.getRandomElement();
                    this.cells[x][y].isAnimating = true;
                    this.cells[x][y].animationType = 'appear';
                }
            }
        }
    }
    
    /**
     * Обробка каскадних збігів
     */
    async processCascadeMatches() {
        let cascadeCount = 0;
        let hasMatches = true;
        
        while (hasMatches) {
            const matches = this.findAllMatches();
            if (matches.length === 0) {
                hasMatches = false;
            } else {
                cascadeCount++;
                await this.processMatches(matches);
                await this.applyGravity();
                await this.fillEmptyCells();
                await this.wait(300); // Пауза між каскадами
            }
        }
        
        return cascadeCount;
    }
    
    /**
     * Запуск системи ротації гравітації
     */
    startGravityRotation() {
        setInterval(() => {
            this.rotateGravity();
        }, this.gravityRotationInterval);
    }
    
    /**
     * Поворот напрямку гравітації
     */
    rotateGravity() {
        const directions = ['down', 'left', 'up', 'right'];
        const currentIndex = directions.indexOf(this.gravityDirection);
        const nextIndex = (currentIndex + 1) % directions.length;
        
        this.gravityDirection = directions[nextIndex];
        console.log(`Гравітація змінена на: ${this.gravityDirection}`);
        
        // Застосування нової гравітації
        this.applyGravity();
    }
    
    /**
     * Додавання нового типу елемента
     */
    addNewElementType() {
        if (this.elementTypes < this.elementArray.length) {
            this.elementTypes++;
            console.log(`Додано новий тип елемента. Всього типів: ${this.elementTypes}`);
        }
    }
    
    /**
     * Оновлення анімацій
     */
    update(deltaTime) {
        // Оновлення анімацій клітинок
        for (let x = 0; x < this.size; x++) {
            for (let y = 0; y < this.size; y++) {
                const cell = this.cells[x][y];
                if (cell.isAnimating) {
                    cell.animationProgress += deltaTime / 300; // 300ms анімація
                    
                    if (cell.animationProgress >= 1) {
                        cell.isAnimating = false;
                        cell.animationType = null;
                        cell.animationProgress = 0;
                    }
                }
            }
        }
    }
    
    /**
     * Перевірка наявності активних анімацій
     */
    hasAnimations() {
        for (let x = 0; x < this.size; x++) {
            for (let y = 0; y < this.size; y++) {
                if (this.cells[x][y].isAnimating) {
                    return true;
                }
            }
        }
        return false;
    }
    
    /**
     * Утилітарні методи
     */
    isValidPosition(x, y) {
        return x >= 0 && x < this.size && y >= 0 && y < this.size;
    }
    
    areAdjacent(x1, y1, x2, y2) {
        const dx = Math.abs(x1 - x2);
        const dy = Math.abs(y1 - y2);
        return (dx === 1 && dy === 0) || (dx === 0 && dy === 1);
    }
    
    getNeighbors(x, y) {
        const neighbors = [];
        const positions = [
            { x: x - 1, y }, { x: x + 1, y },
            { x, y: y - 1 }, { x, y: y + 1 }
        ];
        
        for (const pos of positions) {
            if (this.isValidPosition(pos.x, pos.y)) {
                neighbors.push(pos);
            }
        }
        
        return neighbors;
    }
    
    calculateMatchScore(match) {
        let baseScore = match.length * 10;
        
        // Бонус за великі збіги
        if (match.length >= 5) baseScore *= 2;
        if (match.length >= 7) baseScore *= 3;
        
        return baseScore;
    }
    
    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * Отримання поточного стану сітки
     */
    getState() {
        return {
            cells: this.cells.map(row => row.map(cell => ({
                element: cell.element ? { ...cell.element } : null
            }))),
            gravityDirection: this.gravityDirection,
            elementTypes: this.elementTypes
        };
    }
    
    /**
     * Завантаження стану сітки
     */
    async loadState(state) {
        this.gravityDirection = state.gravityDirection || 'down';
        this.elementTypes = state.elementTypes || 6;

        if (state.cells) {
            for (let x = 0; x < this.size; x++) {
                for (let y = 0; y < this.size; y++) {
                    if (state.cells[x] && state.cells[x][y]) {
                        this.cells[x][y].element = state.cells[x][y].element;
                    }
                }
            }
        }
    }

    /**
     * Перевірка наявності можливих ходів
     */
    hasPossibleMoves() {
        for (let x = 0; x < this.size; x++) {
            for (let y = 0; y < this.size; y++) {
                // Перевірка ходів праворуч та вниз
                const directions = [
                    { dx: 1, dy: 0 },  // праворуч
                    { dx: 0, dy: 1 }   // вниз
                ];

                for (const dir of directions) {
                    const toX = x + dir.dx;
                    const toY = y + dir.dy;

                    if (this.isValidPosition(toX, toY)) {
                        // Симуляція обміну
                        const fromElement = this.cells[x][y].element;
                        const toElement = this.cells[toX][toY].element;

                        this.cells[x][y].element = toElement;
                        this.cells[toX][toY].element = fromElement;

                        // Перевірка збігів
                        const hasMatches = this.hasMatchAt(x, y) || this.hasMatchAt(toX, toY);

                        // Відновлення стану
                        this.cells[x][y].element = fromElement;
                        this.cells[toX][toY].element = toElement;

                        if (hasMatches) {
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }

    /**
     * Отримання підказки для гравця
     */
    getHint() {
        for (let x = 0; x < this.size; x++) {
            for (let y = 0; y < this.size; y++) {
                const directions = [
                    { dx: 1, dy: 0 },  // праворуч
                    { dx: 0, dy: 1 }   // вниз
                ];

                for (const dir of directions) {
                    const toX = x + dir.dx;
                    const toY = y + dir.dy;

                    if (this.isValidPosition(toX, toY)) {
                        // Симуляція обміну
                        const fromElement = this.cells[x][y].element;
                        const toElement = this.cells[toX][toY].element;

                        this.cells[x][y].element = toElement;
                        this.cells[toX][toY].element = fromElement;

                        // Перевірка збігів
                        const hasMatches = this.hasMatchAt(x, y) || this.hasMatchAt(toX, toY);

                        // Відновлення стану
                        this.cells[x][y].element = fromElement;
                        this.cells[toX][toY].element = toElement;

                        if (hasMatches) {
                            return {
                                from: { x, y },
                                to: { x: toX, y: toY }
                            };
                        }
                    }
                }
            }
        }

        return null;
    }

    /**
     * Перемішування сітки
     */
    shuffle() {
        const elements = [];

        // Збір всіх елементів
        for (let x = 0; x < this.size; x++) {
            for (let y = 0; y < this.size; y++) {
                if (this.cells[x][y].element) {
                    elements.push(this.cells[x][y].element);
                }
            }
        }

        // Перемішування Fisher-Yates
        for (let i = elements.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [elements[i], elements[j]] = [elements[j], elements[i]];
        }

        // Розміщення назад на сітці
        let elementIndex = 0;
        for (let x = 0; x < this.size; x++) {
            for (let y = 0; y < this.size; y++) {
                if (elementIndex < elements.length) {
                    this.cells[x][y].element = elements[elementIndex];
                    this.cells[x][y].isAnimating = true;
                    this.cells[x][y].animationType = 'shuffle';
                    elementIndex++;
                }
            }
        }

        console.log('Сітка перемішана');
    }
}
