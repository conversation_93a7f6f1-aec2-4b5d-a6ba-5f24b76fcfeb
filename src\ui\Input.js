/**
 * Клас Input - обробка користувацького вводу
 * Підтримує мишу, дотики та клавіатуру з розпізнаванням жестів
 */

export class Input {
    constructor(game) {
        this.game = game;
        this.canvas = null;
        
        // Стан вводу
        this.isPointerDown = false;
        this.startPosition = { x: 0, y: 0 };
        this.currentPosition = { x: 0, y: 0 };
        this.selectedCell = null;
        this.dragThreshold = 10; // Мінімальна відстань для розпізнавання перетягування
        
        // Підтримка мультитач
        this.touches = new Map();
        this.lastTapTime = 0;
        this.doubleTapThreshold = 300; // мс
        
        // Жести
        this.gestureStartDistance = 0;
        this.gestureStartAngle = 0;
        this.isGesturing = false;
        
        // Прив'язка методів
        this.handlePointerDown = this.handlePointerDown.bind(this);
        this.handlePointerMove = this.handlePointerMove.bind(this);
        this.handlePointerUp = this.handlePointerUp.bind(this);
        this.handleKeyDown = this.handleKeyDown.bind(this);
        this.handleTouchStart = this.handleTouchStart.bind(this);
        this.handleTouchMove = this.handleTouchMove.bind(this);
        this.handleTouchEnd = this.handleTouchEnd.bind(this);
    }
    
    /**
     * Ініціалізація системи вводу
     */
    async initialize() {
        console.log('Ініціалізація системи вводу...');
        
        this.canvas = document.getElementById('gameCanvas');
        if (!this.canvas) {
            throw new Error('Canvas не знайдено для системи вводу');
        }
        
        // Налаштування обробників подій
        this.setupEventListeners();
        
        console.log('Система вводу ініціалізована');
    }
    
    /**
     * Налаштування обробників подій
     */
    setupEventListeners() {
        // Миша
        this.canvas.addEventListener('mousedown', this.handlePointerDown);
        this.canvas.addEventListener('mousemove', this.handlePointerMove);
        this.canvas.addEventListener('mouseup', this.handlePointerUp);
        this.canvas.addEventListener('mouseleave', this.handlePointerUp);
        
        // Дотики
        this.canvas.addEventListener('touchstart', this.handleTouchStart, { passive: false });
        this.canvas.addEventListener('touchmove', this.handleTouchMove, { passive: false });
        this.canvas.addEventListener('touchend', this.handleTouchEnd, { passive: false });
        this.canvas.addEventListener('touchcancel', this.handleTouchEnd, { passive: false });
        
        // Клавіатура
        document.addEventListener('keydown', this.handleKeyDown);
        
        // Запобігання контекстному меню
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
    }
    
    /**
     * Обробка початку натискання/дотику
     */
    handlePointerDown(event) {
        if (this.game.gameState !== 'playing') return;
        
        event.preventDefault();
        
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        this.startPointerInteraction(x, y);
    }
    
    /**
     * Обробка руху миші/дотику
     */
    handlePointerMove(event) {
        if (!this.isPointerDown || this.game.gameState !== 'playing') return;
        
        event.preventDefault();
        
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        this.updatePointerInteraction(x, y);
    }
    
    /**
     * Обробка завершення натискання/дотику
     */
    handlePointerUp(event) {
        if (!this.isPointerDown) return;
        
        event.preventDefault();
        this.endPointerInteraction();
    }
    
    /**
     * Обробка початку дотику
     */
    handleTouchStart(event) {
        if (this.game.gameState !== 'playing') return;
        
        event.preventDefault();
        
        const touches = event.changedTouches;
        
        if (touches.length === 1) {
            // Одиночний дотик
            const touch = touches[0];
            const rect = this.canvas.getBoundingClientRect();
            const x = touch.clientX - rect.left;
            const y = touch.clientY - rect.top;
            
            // Перевірка на подвійний тап
            const currentTime = Date.now();
            if (currentTime - this.lastTapTime < this.doubleTapThreshold) {
                this.handleDoubleTap(x, y);
                return;
            }
            this.lastTapTime = currentTime;
            
            this.startPointerInteraction(x, y);
            
        } else if (touches.length === 2) {
            // Жести двома пальцями
            this.startGesture(touches);
        }
        
        // Збереження інформації про дотики
        for (let i = 0; i < touches.length; i++) {
            const touch = touches[i];
            this.touches.set(touch.identifier, {
                x: touch.clientX,
                y: touch.clientY,
                startTime: Date.now()
            });
        }
    }
    
    /**
     * Обробка руху дотику
     */
    handleTouchMove(event) {
        if (this.game.gameState !== 'playing') return;
        
        event.preventDefault();
        
        const touches = event.changedTouches;
        
        if (touches.length === 1 && this.isPointerDown) {
            const touch = touches[0];
            const rect = this.canvas.getBoundingClientRect();
            const x = touch.clientX - rect.left;
            const y = touch.clientY - rect.top;
            
            this.updatePointerInteraction(x, y);
            
        } else if (touches.length === 2 && this.isGesturing) {
            this.updateGesture(touches);
        }
    }
    
    /**
     * Обробка завершення дотику
     */
    handleTouchEnd(event) {
        event.preventDefault();
        
        const touches = event.changedTouches;
        
        // Видалення завершених дотиків
        for (let i = 0; i < touches.length; i++) {
            const touch = touches[i];
            this.touches.delete(touch.identifier);
        }
        
        // Якщо всі дотики завершені
        if (this.touches.size === 0) {
            this.endPointerInteraction();
            this.isGesturing = false;
        }
    }
    
    /**
     * Початок взаємодії з вказівником
     */
    startPointerInteraction(x, y) {
        this.isPointerDown = true;
        this.startPosition = { x, y };
        this.currentPosition = { x, y };
        
        // Конвертація в координати сітки
        const gridPos = this.game.renderer.screenToGrid(x, y);
        
        if (this.game.grid.isValidPosition(gridPos.x, gridPos.y)) {
            // Вибір клітинки
            this.selectCell(gridPos.x, gridPos.y);
            
            // Тактильний відгук на мобільних пристроях
            if ('vibrate' in navigator) {
                navigator.vibrate(50);
            }
        }
    }
    
    /**
     * Оновлення взаємодії з вказівником
     */
    updatePointerInteraction(x, y) {
        this.currentPosition = { x, y };
        
        // Перевірка чи це перетягування
        const distance = Math.sqrt(
            Math.pow(x - this.startPosition.x, 2) + 
            Math.pow(y - this.startPosition.y, 2)
        );
        
        if (distance > this.dragThreshold && this.selectedCell) {
            // Визначення напрямку перетягування
            const direction = this.getDragDirection(
                this.startPosition.x, this.startPosition.y, x, y
            );
            
            if (direction) {
                this.handleDrag(direction);
            }
        }
    }
    
    /**
     * Завершення взаємодії з вказівником
     */
    endPointerInteraction() {
        if (this.selectedCell) {
            // Зняття виділення
            this.deselectCell();
        }
        
        this.isPointerDown = false;
        this.selectedCell = null;
    }
    
    /**
     * Вибір клітинки
     */
    selectCell(x, y) {
        // Зняття попереднього виділення
        if (this.selectedCell) {
            this.game.grid.cells[this.selectedCell.x][this.selectedCell.y].isSelected = false;
        }
        
        // Встановлення нового виділення
        this.selectedCell = { x, y };
        this.game.grid.cells[x][y].isSelected = true;
        
        // Звуковий ефект
        this.game.soundManager.playSound('button');
        
        console.log(`Клітинка вибрана: (${x}, ${y})`);
    }
    
    /**
     * Зняття виділення клітинки
     */
    deselectCell() {
        if (this.selectedCell) {
            const { x, y } = this.selectedCell;
            this.game.grid.cells[x][y].isSelected = false;
            this.selectedCell = null;
        }
    }
    
    /**
     * Визначення напрямку перетягування
     */
    getDragDirection(startX, startY, endX, endY) {
        const dx = endX - startX;
        const dy = endY - startY;
        const absDx = Math.abs(dx);
        const absDy = Math.abs(dy);
        
        // Визначення основного напрямку
        if (absDx > absDy) {
            return dx > 0 ? 'right' : 'left';
        } else {
            return dy > 0 ? 'down' : 'up';
        }
    }
    
    /**
     * Обробка перетягування
     */
    handleDrag(direction) {
        if (!this.selectedCell) return;
        
        const { x, y } = this.selectedCell;
        let targetX = x;
        let targetY = y;
        
        // Визначення цільової клітинки
        switch (direction) {
            case 'up':
                targetY = y - 1;
                break;
            case 'down':
                targetY = y + 1;
                break;
            case 'left':
                targetX = x - 1;
                break;
            case 'right':
                targetX = x + 1;
                break;
        }
        
        // Виконання ходу
        if (this.game.grid.isValidPosition(targetX, targetY)) {
            this.game.makeMove(x, y, targetX, targetY);
        }
        
        // Зняття виділення після ходу
        this.deselectCell();
    }
    
    /**
     * Обробка подвійного тапу
     */
    handleDoubleTap(x, y) {
        const gridPos = this.game.renderer.screenToGrid(x, y);
        
        if (this.game.grid.isValidPosition(gridPos.x, gridPos.y)) {
            // Подвійний тап може активувати спеціальну здатність
            this.game.interface.showNotification('Подвійний тап!', 'info', 1000);
            
            // Тактильний відгук
            if ('vibrate' in navigator) {
                navigator.vibrate([50, 50, 50]);
            }
        }
    }
    
    /**
     * Початок жесту двома пальцями
     */
    startGesture(touches) {
        if (touches.length !== 2) return;
        
        const touch1 = touches[0];
        const touch2 = touches[1];
        
        // Розрахунок початкової відстані та кута
        this.gestureStartDistance = this.calculateDistance(touch1, touch2);
        this.gestureStartAngle = this.calculateAngle(touch1, touch2);
        this.isGesturing = true;
        
        console.log('Початок жесту двома пальцями');
    }
    
    /**
     * Оновлення жесту двома пальцями
     */
    updateGesture(touches) {
        if (touches.length !== 2 || !this.isGesturing) return;
        
        const touch1 = touches[0];
        const touch2 = touches[1];
        
        const currentDistance = this.calculateDistance(touch1, touch2);
        const currentAngle = this.calculateAngle(touch1, touch2);
        
        // Розпізнавання pinch (масштабування)
        const scaleChange = currentDistance / this.gestureStartDistance;
        if (Math.abs(scaleChange - 1) > 0.1) {
            this.handlePinchGesture(scaleChange);
        }
        
        // Розпізнавання обертання
        const angleChange = currentAngle - this.gestureStartAngle;
        if (Math.abs(angleChange) > 0.3) { // ~17 градусів
            this.handleRotationGesture(angleChange);
        }
    }
    
    /**
     * Обробка жесту масштабування
     */
    handlePinchGesture(scale) {
        if (scale > 1.2) {
            // Збільшення - можна додати zoom функціональність
            this.game.interface.showNotification('Жест збільшення', 'info', 1000);
        } else if (scale < 0.8) {
            // Зменшення
            this.game.interface.showNotification('Жест зменшення', 'info', 1000);
        }
    }
    
    /**
     * Обробка жесту обертання
     */
    handleRotationGesture(angle) {
        // Обертання може змінювати напрямок гравітації
        if (Math.abs(angle) > 0.5) { // ~30 градусів
            this.game.grid.rotateGravity();
            this.game.interface.showNotification('Гравітація змінена!', 'success', 2000);
            this.game.soundManager.playSound('gravity');
            
            // Скидання жесту
            this.gestureStartAngle = angle;
        }
    }
    
    /**
     * Розрахунок відстані між двома точками
     */
    calculateDistance(point1, point2) {
        const dx = point1.clientX - point2.clientX;
        const dy = point1.clientY - point2.clientY;
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    /**
     * Розрахунок кута між двома точками
     */
    calculateAngle(point1, point2) {
        const dx = point1.clientX - point2.clientX;
        const dy = point1.clientY - point2.clientY;
        return Math.atan2(dy, dx);
    }
    
    /**
     * Обробка клавіатурного вводу
     */
    handleKeyDown(event) {
        if (this.game.gameState !== 'playing') return;
        
        // Навігація клавіатурою для доступності
        if (this.selectedCell) {
            let newX = this.selectedCell.x;
            let newY = this.selectedCell.y;
            
            switch (event.key) {
                case 'ArrowUp':
                    newY = Math.max(0, newY - 1);
                    event.preventDefault();
                    break;
                case 'ArrowDown':
                    newY = Math.min(this.game.grid.size - 1, newY + 1);
                    event.preventDefault();
                    break;
                case 'ArrowLeft':
                    newX = Math.max(0, newX - 1);
                    event.preventDefault();
                    break;
                case 'ArrowRight':
                    newX = Math.min(this.game.grid.size - 1, newX + 1);
                    event.preventDefault();
                    break;
                case 'Enter':
                case ' ':
                    // Активація вибраної клітинки
                    this.activateSelectedCell();
                    event.preventDefault();
                    break;
                case 'Escape':
                    // Зняття виділення
                    this.deselectCell();
                    event.preventDefault();
                    break;
            }
            
            // Переміщення виділення
            if (newX !== this.selectedCell.x || newY !== this.selectedCell.y) {
                this.selectCell(newX, newY);
            }
        } else {
            // Якщо немає виділеної клітинки, вибрати центральну
            if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
                const centerX = Math.floor(this.game.grid.size / 2);
                const centerY = Math.floor(this.game.grid.size / 2);
                this.selectCell(centerX, centerY);
                event.preventDefault();
            }
        }
    }
    
    /**
     * Активація вибраної клітинки
     */
    activateSelectedCell() {
        if (!this.selectedCell) return;
        
        // Логіка активації (наприклад, спеціальна здатність)
        this.game.interface.showNotification(
            `Активовано клітинку (${this.selectedCell.x + 1}, ${this.selectedCell.y + 1})`,
            'info',
            1500
        );
    }
    
    /**
     * Отримання координат дотику відносно canvas
     */
    getTouchPosition(touch) {
        const rect = this.canvas.getBoundingClientRect();
        return {
            x: touch.clientX - rect.left,
            y: touch.clientY - rect.top
        };
    }
    
    /**
     * Перевірка чи координати знаходяться в межах canvas
     */
    isWithinCanvas(x, y) {
        return x >= 0 && y >= 0 && x <= this.canvas.width && y <= this.canvas.height;
    }
    
    /**
     * Розпізнавання свайпу
     */
    recognizeSwipe(startX, startY, endX, endY, duration) {
        const distance = Math.sqrt(
            Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2)
        );
        
        const velocity = distance / duration; // пікселів за мілісекунду
        const minSwipeDistance = 50;
        const minSwipeVelocity = 0.3;
        
        if (distance < minSwipeDistance || velocity < minSwipeVelocity) {
            return null;
        }
        
        const angle = Math.atan2(endY - startY, endX - startX);
        const degrees = angle * 180 / Math.PI;
        
        // Визначення напрямку свайпу
        if (degrees >= -45 && degrees <= 45) return 'right';
        if (degrees >= 45 && degrees <= 135) return 'down';
        if (degrees >= 135 || degrees <= -135) return 'left';
        if (degrees >= -135 && degrees <= -45) return 'up';
        
        return null;
    }
    
    /**
     * Обробка свайпу
     */
    handleSwipe(direction) {
        console.log(`Свайп в напрямку: ${direction}`);
        
        // Свайп може змінювати напрямок гравітації
        if (direction !== this.game.grid.gravityDirection) {
            this.game.grid.gravityDirection = direction;
            this.game.grid.applyGravity();
            this.game.interface.showNotification(`Гравітація: ${direction}`, 'info', 2000);
            this.game.soundManager.playSound('gravity');
        }
    }
    
    /**
     * Увімкнення/вимкнення системи вводу
     */
    setEnabled(enabled) {
        this.canvas.style.pointerEvents = enabled ? 'auto' : 'none';
    }
    
    /**
     * Очищення ресурсів
     */
    destroy() {
        // Видалення обробників подій
        this.canvas.removeEventListener('mousedown', this.handlePointerDown);
        this.canvas.removeEventListener('mousemove', this.handlePointerMove);
        this.canvas.removeEventListener('mouseup', this.handlePointerUp);
        this.canvas.removeEventListener('touchstart', this.handleTouchStart);
        this.canvas.removeEventListener('touchmove', this.handleTouchMove);
        this.canvas.removeEventListener('touchend', this.handleTouchEnd);
        document.removeEventListener('keydown', this.handleKeyDown);
        
        console.log('Система вводу очищена');
    }
}
