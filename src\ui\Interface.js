/**
 * К<PERSON>ас Interface - управління користувацьким інтерфейсом
 * Відповідає за оновлення статистики, меню та повідомлення
 */

export class Interface {
    constructor(game) {
        this.game = game;
        
        // DOM елементи
        this.elements = {
            scoreDisplay: null,
            levelDisplay: null,
            movesDisplay: null,
            pauseBtn: null,
            hintBtn: null,
            shuffleBtn: null,
            settingsBtn: null
        };
        
        // Стан інтерфейсу
        this.isPaused = false;
        this.isMenuOpen = false;
        
        // Анімації повідомлень
        this.notifications = [];
        
        // Налаштування
        this.settings = {
            soundEnabled: true,
            musicEnabled: true,
            vibrationEnabled: true,
            theme: 'default'
        };
    }
    
    /**
     * Ініціалізація інтерфейсу
     */
    async initialize() {
        console.log('Ініціалізація інтерфейсу...');
        
        // Отримання посилань на DOM елементи
        this.elements.scoreDisplay = document.getElementById('scoreDisplay');
        this.elements.levelDisplay = document.getElementById('levelDisplay');
        this.elements.movesDisplay = document.getElementById('movesDisplay');
        this.elements.pauseBtn = document.getElementById('pauseBtn');
        this.elements.hintBtn = document.getElementById('hintBtn');
        this.elements.shuffleBtn = document.getElementById('shuffleBtn');
        this.elements.settingsBtn = document.getElementById('settingsBtn');
        
        // Перевірка наявності елементів
        for (const [name, element] of Object.entries(this.elements)) {
            if (!element) {
                console.warn(`Елемент інтерфейсу "${name}" не знайдено`);
            }
        }
        
        // Налаштування обробників подій
        this.setupEventListeners();
        
        // Завантаження налаштувань
        await this.loadSettings();
        
        console.log('Інтерфейс ініціалізований');
    }
    
    /**
     * Налаштування обробників подій
     */
    setupEventListeners() {
        // Кнопка паузи
        if (this.elements.pauseBtn) {
            this.elements.pauseBtn.addEventListener('click', () => {
                this.togglePause();
                this.game.soundManager.playSound('button');
            });
        }
        
        // Кнопка підказки
        if (this.elements.hintBtn) {
            this.elements.hintBtn.addEventListener('click', () => {
                this.showHint();
                this.game.soundManager.playSound('button');
            });
        }
        
        // Кнопка перемішування
        if (this.elements.shuffleBtn) {
            this.elements.shuffleBtn.addEventListener('click', () => {
                this.shuffleGrid();
                this.game.soundManager.playSound('button');
            });
        }
        
        // Кнопка налаштувань
        if (this.elements.settingsBtn) {
            this.elements.settingsBtn.addEventListener('click', () => {
                this.toggleSettings();
                this.game.soundManager.playSound('button');
            });
        }
        
        // Обробка клавіатурних скорочень
        document.addEventListener('keydown', (event) => {
            this.handleKeyboard(event);
        });
    }
    
    /**
     * Оновлення статистики гри
     */
    updateStats(stats) {
        if (stats.score !== undefined && this.elements.scoreDisplay) {
            this.elements.scoreDisplay.textContent = `Очки: ${stats.score.toLocaleString('uk-UA')}`;
            
            // Анімація збільшення очок
            if (stats.score > this.lastScore) {
                this.animateScoreIncrease(stats.score - this.lastScore);
            }
            this.lastScore = stats.score;
        }
        
        if (stats.level !== undefined && this.elements.levelDisplay) {
            this.elements.levelDisplay.textContent = `Рівень: ${stats.level}`;
        }
        
        if (stats.moves !== undefined && this.elements.movesDisplay) {
            this.elements.movesDisplay.textContent = `Ходи: ${stats.moves}`;
            
            // Зміна кольору при малій кількості ходів
            if (stats.moves <= 5) {
                this.elements.movesDisplay.style.color = '#e74c3c';
            } else if (stats.moves <= 10) {
                this.elements.movesDisplay.style.color = '#f39c12';
            } else {
                this.elements.movesDisplay.style.color = 'white';
            }
        }
        
        if (stats.targetScore !== undefined) {
            this.targetScore = stats.targetScore;
        }
    }
    
    /**
     * Анімація збільшення очок
     */
    animateScoreIncrease(increase) {
        // Створення тимчасового елемента для анімації
        const scorePopup = document.createElement('div');
        scorePopup.textContent = `+${increase}`;
        scorePopup.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #27ae60;
            font-size: 1.5rem;
            font-weight: bold;
            pointer-events: none;
            z-index: 1000;
            text-shadow: 0 1px 2px rgba(0,0,0,0.5);
        `;
        
        document.body.appendChild(scorePopup);
        
        // Анімація з GSAP (якщо доступний)
        if (typeof gsap !== 'undefined') {
            gsap.fromTo(scorePopup, 
                { y: 0, opacity: 1, scale: 0.5 },
                { 
                    y: -50, 
                    opacity: 0, 
                    scale: 1.2, 
                    duration: 1,
                    ease: 'power2.out',
                    onComplete: () => document.body.removeChild(scorePopup)
                }
            );
        } else {
            // Fallback анімація
            setTimeout(() => {
                if (scorePopup.parentNode) {
                    document.body.removeChild(scorePopup);
                }
            }, 1000);
        }
    }
    
    /**
     * Перемикання паузи
     */
    togglePause() {
        if (this.isPaused) {
            this.game.resume();
            this.elements.pauseBtn.textContent = '⏸️';
            this.elements.pauseBtn.title = 'Пауза';
        } else {
            this.game.pause();
            this.elements.pauseBtn.textContent = '▶️';
            this.elements.pauseBtn.title = 'Продовжити';
        }
        
        this.isPaused = !this.isPaused;
    }
    
    /**
     * Показ підказки
     */
    showHint() {
        const hint = this.game.rules.getHint();
        
        if (!hint) {
            this.showNotification('Немає доступних ходів. Спробуйте перемішати!', 'warning');
            return;
        }
        
        // Підсвічування рекомендованого ходу
        const { from, to } = hint;
        this.highlightCells([from, to], 2000);
        
        this.showNotification(`Спробуйте хід: (${from.x+1},${from.y+1}) → (${to.x+1},${to.y+1})`, 'info');
    }
    
    /**
     * Перемішування сітки
     */
    shuffleGrid() {
        if (this.game.moves <= 0) {
            this.showNotification('Неможливо перемішати - ходи закінчилися!', 'warning');
            return;
        }
        
        this.game.rules.shuffleGrid();
        this.game.moves = Math.max(0, this.game.moves - 1); // Штраф за перемішування
        
        this.updateStats({ moves: this.game.moves });
        this.showNotification('Сітка перемішана!', 'success');
    }
    
    /**
     * Перемикання меню налаштувань
     */
    toggleSettings() {
        if (this.isMenuOpen) {
            this.hideSettingsMenu();
        } else {
            this.showSettingsMenu();
        }
    }
    
    /**
     * Показ меню налаштувань
     */
    showSettingsMenu() {
        // Створення модального вікна налаштувань
        const modal = document.createElement('div');
        modal.id = 'settingsModal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
        `;
        
        const content = document.createElement('div');
        content.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 400px;
            width: 90%;
            max-height: 80%;
            overflow-y: auto;
        `;
        
        content.innerHTML = `
            <h2 style="margin-bottom: 20px; color: #2c3e50;">Налаштування</h2>
            
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px;">Звукові ефекти:</label>
                <input type="range" id="sfxVolume" min="0" max="100" value="${this.game.soundManager.getVolume('sfx') * 100}">
            </div>
            
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px;">Фонова музика:</label>
                <input type="range" id="musicVolume" min="0" max="100" value="${this.game.soundManager.getVolume('music') * 100}">
            </div>
            
            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 5px;">Загальна гучність:</label>
                <input type="range" id="masterVolume" min="0" max="100" value="${this.game.soundManager.getVolume('master') * 100}">
            </div>
            
            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                <button id="resetGameBtn" style="padding: 10px 20px; background: #e74c3c; color: white; border: none; border-radius: 5px;">Нова гра</button>
                <button id="closeSettingsBtn" style="padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 5px;">Закрити</button>
            </div>
        `;
        
        modal.appendChild(content);
        document.body.appendChild(modal);
        
        // Обробники подій для налаштувань
        document.getElementById('sfxVolume').addEventListener('input', (e) => {
            this.game.soundManager.setVolume('sfx', e.target.value / 100);
        });
        
        document.getElementById('musicVolume').addEventListener('input', (e) => {
            this.game.soundManager.setVolume('music', e.target.value / 100);
        });
        
        document.getElementById('masterVolume').addEventListener('input', (e) => {
            this.game.soundManager.setVolume('master', e.target.value / 100);
        });
        
        document.getElementById('resetGameBtn').addEventListener('click', () => {
            this.hideSettingsMenu();
            this.game.startNewGame();
        });
        
        document.getElementById('closeSettingsBtn').addEventListener('click', () => {
            this.hideSettingsMenu();
        });
        
        // Закриття при кліку поза модальним вікном
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.hideSettingsMenu();
            }
        });
        
        this.isMenuOpen = true;
    }
    
    /**
     * Приховування меню налаштувань
     */
    hideSettingsMenu() {
        const modal = document.getElementById('settingsModal');
        if (modal) {
            document.body.removeChild(modal);
        }
        this.isMenuOpen = false;
    }
    
    /**
     * Показ повідомлення
     */
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 15px 25px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1500;
            max-width: 90%;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        
        // Кольори залежно від типу
        switch (type) {
            case 'success':
                notification.style.background = '#27ae60';
                break;
            case 'warning':
                notification.style.background = '#f39c12';
                break;
            case 'error':
                notification.style.background = '#e74c3c';
                break;
            default:
                notification.style.background = '#3498db';
        }
        
        notification.textContent = message;
        document.body.appendChild(notification);
        
        // Анімація появи
        if (typeof gsap !== 'undefined') {
            gsap.fromTo(notification,
                { y: -50, opacity: 0 },
                { y: 0, opacity: 1, duration: 0.3, ease: 'back.out(1.7)' }
            );
            
            // Анімація зникнення
            setTimeout(() => {
                gsap.to(notification, {
                    y: -50,
                    opacity: 0,
                    duration: 0.3,
                    ease: 'back.in(1.7)',
                    onComplete: () => {
                        if (notification.parentNode) {
                            document.body.removeChild(notification);
                        }
                    }
                });
            }, duration);
        } else {
            // Fallback без анімації
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, duration);
        }
    }
    
    /**
     * Підсвічування клітинок
     */
    highlightCells(positions, duration = 1000) {
        // Позначення клітинок як підсвічених
        positions.forEach(pos => {
            if (this.game.grid.isValidPosition(pos.x, pos.y)) {
                this.game.grid.cells[pos.x][pos.y].isHighlighted = true;
            }
        });
        
        // Автоматичне зняття підсвічування
        setTimeout(() => {
            positions.forEach(pos => {
                if (this.game.grid.isValidPosition(pos.x, pos.y)) {
                    this.game.grid.cells[pos.x][pos.y].isHighlighted = false;
                }
            });
        }, duration);
    }
    
    /**
     * Показ екрану завершення гри
     */
    showGameOver(finalScore, finalLevel) {
        const modal = document.createElement('div');
        modal.id = 'gameOverModal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
        `;
        
        const content = document.createElement('div');
        content.style.cssText = `
            background: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        `;
        
        content.innerHTML = `
            <h2 style="color: #e74c3c; margin-bottom: 20px; font-size: 2rem;">Гра завершена!</h2>
            <div style="margin-bottom: 30px; color: #2c3e50;">
                <div style="font-size: 1.2rem; margin-bottom: 10px;">Фінальний рахунок: <strong>${finalScore.toLocaleString('uk-UA')}</strong></div>
                <div style="font-size: 1rem;">Досягнутий рівень: <strong>${finalLevel}</strong></div>
            </div>
            <div style="display: flex; gap: 15px; justify-content: center;">
                <button id="newGameBtn" style="padding: 12px 24px; background: #27ae60; color: white; border: none; border-radius: 8px; font-size: 1rem; cursor: pointer;">Нова гра</button>
                <button id="continueBtn" style="padding: 12px 24px; background: #3498db; color: white; border: none; border-radius: 8px; font-size: 1rem; cursor: pointer;">Продовжити</button>
            </div>
        `;
        
        modal.appendChild(content);
        document.body.appendChild(modal);
        
        // Обробники подій
        document.getElementById('newGameBtn').addEventListener('click', () => {
            document.body.removeChild(modal);
            this.game.startNewGame();
        });
        
        document.getElementById('continueBtn').addEventListener('click', () => {
            document.body.removeChild(modal);
            // Додавання додаткових ходів для продовження
            this.game.moves += 10;
            this.updateStats({ moves: this.game.moves });
            this.game.gameState = 'playing';
        });
        
        // Анімація появи
        if (typeof gsap !== 'undefined') {
            gsap.fromTo(content,
                { scale: 0.5, opacity: 0 },
                { scale: 1, opacity: 1, duration: 0.5, ease: 'back.out(1.7)' }
            );
        }
    }
    
    /**
     * Обробка клавіатурних скорочень
     */
    handleKeyboard(event) {
        switch (event.key) {
            case ' ':
            case 'Escape':
                event.preventDefault();
                this.togglePause();
                break;
            case 'h':
            case 'H':
                event.preventDefault();
                this.showHint();
                break;
            case 's':
            case 'S':
                event.preventDefault();
                this.shuffleGrid();
                break;
            case 'm':
            case 'M':
                event.preventDefault();
                this.game.soundManager.toggleMute();
                this.showNotification(
                    this.game.soundManager.isMuted() ? 'Звук вимкнено' : 'Звук увімкнено',
                    'info',
                    1000
                );
                break;
        }
    }
    
    /**
     * Обробка зміни розміру
     */
    handleResize() {
        // Оновлення розмірів інтерфейсних елементів при необхідності
        console.log('Інтерфейс адаптовано до нового розміру');
    }
    
    /**
     * Завантаження налаштувань
     */
    async loadSettings() {
        try {
            const savedSettings = localStorage.getItem('gameSettings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                Object.assign(this.settings, settings);
                
                // Застосування налаштувань
                this.applySettings();
            }
        } catch (error) {
            console.warn('Помилка завантаження налаштувань:', error);
        }
    }
    
    /**
     * Збереження налаштувань
     */
    async saveSettings() {
        try {
            localStorage.setItem('gameSettings', JSON.stringify(this.settings));
        } catch (error) {
            console.warn('Помилка збереження налаштувань:', error);
        }
    }
    
    /**
     * Застосування налаштувань
     */
    applySettings() {
        // Застосування налаштувань звуку
        if (!this.settings.soundEnabled) {
            this.game.soundManager.setVolume('sfx', 0);
        }
        
        if (!this.settings.musicEnabled) {
            this.game.soundManager.setVolume('music', 0);
        }
        
        // Застосування теми
        if (this.settings.theme === 'dark') {
            document.body.classList.add('dark-theme');
        }
    }
    
    /**
     * Показ екрану перемоги рівня
     */
    showLevelComplete(level, score, bonus) {
        this.showNotification(`Рівень ${level} завершено! Бонус: ${bonus}`, 'success', 4000);
    }
    
    /**
     * Оновлення прогрес-бару
     */
    updateProgress(current, target) {
        const percentage = Math.min(100, (current / target) * 100);
        
        // Можна додати візуальний прогрес-бар пізніше
        console.log(`Прогрес: ${percentage.toFixed(1)}%`);
    }
}
