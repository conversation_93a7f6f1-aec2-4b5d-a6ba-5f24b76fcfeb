/**
 * К<PERSON><PERSON><PERSON> SoundManager - управління аудіо системою гри
 * Реалізує процедурну генерацію музики та звукових ефектів
 */

import { MusicGenerator } from './MusicGen.js';
import { SFXGenerator } from './SFXGen.js';

export class SoundManager {
    constructor() {
        this.audioContext = null;
        this.masterGain = null;
        this.musicGain = null;
        this.sfxGain = null;
        
        // Налаштування гучності
        this.volumes = {
            master: 0.7,
            music: 0.4,
            sfx: 0.6
        };
        
        // Стан аудіо
        this.isInitialized = false;
        this.isMusicPlaying = false;
        this.currentTrack = null;
        
        // Музичні параметри
        this.musicParams = {
            bpm: 120,
            key: 'C',
            scale: ['C', 'D', 'E', 'F', 'G', 'A', 'B'],
            chordProgression: ['C', 'Am', 'F', 'G'],
            currentChord: 0
        };
        
        // Генератори
        this.musicGenerator = null;
        this.sfxGenerator = null;

        // Кеш звукових ефектів
        this.sfxCache = new Map();
    }
    
    /**
     * Ініціалізація аудіо системи
     */
    async initialize() {
        console.log('Ініціалізація аудіо системи...');
        
        try {
            // Створення аудіо контексту
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Створення головних вузлів гучності
            this.masterGain = this.audioContext.createGain();
            this.musicGain = this.audioContext.createGain();
            this.sfxGain = this.audioContext.createGain();
            
            // Підключення вузлів
            this.musicGain.connect(this.masterGain);
            this.sfxGain.connect(this.masterGain);
            this.masterGain.connect(this.audioContext.destination);
            
            // Встановлення початкової гучності
            this.updateVolumes();
            
            // Ініціалізація генераторів
            this.musicGenerator = new MusicGenerator(this.audioContext);
            await this.musicGenerator.initialize();

            this.sfxGenerator = new SFXGenerator(this.audioContext);

            // Ініціалізація кешу звукових ефектів
            await this.initializeSFXCache();

            this.isInitialized = true;
            console.log('Аудіо система ініціалізована');
            
        } catch (error) {
            console.error('Помилка ініціалізації аудіо:', error);
            this.isInitialized = false;
        }
    }
    
    /**
     * Ініціалізація кешу звукових ефектів
     */
    async initializeSFXCache() {
        const sfxTypes = [
            'match', 'invalidMove', 'levelUp', 'gameOver', 
            'reaction', 'cascade', 'gravity', 'button'
        ];
        
        for (const type of sfxTypes) {
            this.sfxCache.set(type, this.createSFXGenerator(type));
        }
    }
    
    /**
     * Створення генератора звукових ефектів
     */
    createSFXGenerator(type) {
        return () => {
            if (!this.isInitialized) return;
            
            const now = this.audioContext.currentTime;
            
            switch (type) {
                case 'match':
                    this.playMatchSound(now);
                    break;
                case 'invalidMove':
                    this.playInvalidMoveSound(now);
                    break;
                case 'levelUp':
                    this.playLevelUpSound(now);
                    break;
                case 'gameOver':
                    this.playGameOverSound(now);
                    break;
                case 'reaction':
                    this.playReactionSound(now);
                    break;
                case 'cascade':
                    this.playCascadeSound(now);
                    break;
                case 'gravity':
                    this.playGravitySound(now);
                    break;
                case 'button':
                    this.playButtonSound(now);
                    break;
            }
        };
    }
    
    /**
     * Відтворення звуку збігу
     */
    playMatchSound(startTime) {
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        
        osc.connect(gain);
        gain.connect(this.sfxGain);
        
        // Параметри звуку
        osc.type = 'sine';
        osc.frequency.setValueAtTime(440, startTime);
        osc.frequency.exponentialRampToValueAtTime(880, startTime + 0.1);
        
        gain.gain.setValueAtTime(0.3, startTime);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + 0.3);
        
        osc.start(startTime);
        osc.stop(startTime + 0.3);
    }
    
    /**
     * Відтворення звуку неправильного ходу
     */
    playInvalidMoveSound(startTime) {
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        
        osc.connect(gain);
        gain.connect(this.sfxGain);
        
        osc.type = 'sawtooth';
        osc.frequency.setValueAtTime(200, startTime);
        osc.frequency.linearRampToValueAtTime(150, startTime + 0.2);
        
        gain.gain.setValueAtTime(0.2, startTime);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + 0.2);
        
        osc.start(startTime);
        osc.stop(startTime + 0.2);
    }
    
    /**
     * Відтворення звуку переходу на рівень
     */
    playLevelUpSound(startTime) {
        // Арпеджіо для святкового звуку
        const notes = [261.63, 329.63, 392.00, 523.25]; // C, E, G, C
        
        notes.forEach((freq, index) => {
            const osc = this.audioContext.createOscillator();
            const gain = this.audioContext.createGain();
            
            osc.connect(gain);
            gain.connect(this.sfxGain);
            
            osc.type = 'triangle';
            osc.frequency.value = freq;
            
            const noteStart = startTime + index * 0.1;
            gain.gain.setValueAtTime(0.4, noteStart);
            gain.gain.exponentialRampToValueAtTime(0.01, noteStart + 0.5);
            
            osc.start(noteStart);
            osc.stop(noteStart + 0.5);
        });
    }
    
    /**
     * Відтворення звуку завершення гри
     */
    playGameOverSound(startTime) {
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        
        osc.connect(gain);
        gain.connect(this.sfxGain);
        
        osc.type = 'square';
        osc.frequency.setValueAtTime(440, startTime);
        osc.frequency.exponentialRampToValueAtTime(220, startTime + 1.0);
        
        gain.gain.setValueAtTime(0.3, startTime);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + 1.0);
        
        osc.start(startTime);
        osc.stop(startTime + 1.0);
    }
    
    /**
     * Відтворення звуку хімічної реакції
     */
    playReactionSound(startTime) {
        // Складний звук з модуляцією
        const carrier = this.audioContext.createOscillator();
        const modulator = this.audioContext.createOscillator();
        const modulatorGain = this.audioContext.createGain();
        const gain = this.audioContext.createGain();
        
        modulator.connect(modulatorGain);
        modulatorGain.connect(carrier.frequency);
        carrier.connect(gain);
        gain.connect(this.sfxGain);
        
        carrier.type = 'sine';
        carrier.frequency.value = 300;
        modulator.type = 'sine';
        modulator.frequency.value = 5;
        modulatorGain.gain.value = 50;
        
        gain.gain.setValueAtTime(0.4, startTime);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + 0.8);
        
        carrier.start(startTime);
        modulator.start(startTime);
        carrier.stop(startTime + 0.8);
        modulator.stop(startTime + 0.8);
    }
    
    /**
     * Відтворення звуку каскаду
     */
    playCascadeSound(startTime) {
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        const filter = this.audioContext.createBiquadFilter();
        
        osc.connect(filter);
        filter.connect(gain);
        gain.connect(this.sfxGain);
        
        osc.type = 'sawtooth';
        osc.frequency.setValueAtTime(220, startTime);
        osc.frequency.exponentialRampToValueAtTime(440, startTime + 0.3);
        
        filter.type = 'lowpass';
        filter.frequency.setValueAtTime(1000, startTime);
        filter.frequency.exponentialRampToValueAtTime(2000, startTime + 0.3);
        
        gain.gain.setValueAtTime(0.3, startTime);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + 0.3);
        
        osc.start(startTime);
        osc.stop(startTime + 0.3);
    }
    
    /**
     * Відтворення звуку зміни гравітації
     */
    playGravitySound(startTime) {
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        
        osc.connect(gain);
        gain.connect(this.sfxGain);
        
        osc.type = 'triangle';
        osc.frequency.setValueAtTime(150, startTime);
        osc.frequency.linearRampToValueAtTime(300, startTime + 0.2);
        osc.frequency.linearRampToValueAtTime(150, startTime + 0.4);
        
        gain.gain.setValueAtTime(0.3, startTime);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + 0.4);
        
        osc.start(startTime);
        osc.stop(startTime + 0.4);
    }
    
    /**
     * Відтворення звуку кнопки
     */
    playButtonSound(startTime) {
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        
        osc.connect(gain);
        gain.connect(this.sfxGain);
        
        osc.type = 'sine';
        osc.frequency.value = 800;
        
        gain.gain.setValueAtTime(0.2, startTime);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + 0.1);
        
        osc.start(startTime);
        osc.stop(startTime + 0.1);
    }
    
    /**
     * Відтворення фонової музики
     */
    playBackgroundMusic() {
        if (!this.isInitialized || this.isMusicPlaying) return;

        this.isMusicPlaying = true;

        if (this.musicGenerator) {
            this.musicGenerator.start();
        }

        console.log('Фонова музика запущена');
    }
    
    /**
     * Планування наступної ноти
     */
    scheduleNextNote() {
        if (!this.isMusicPlaying) return;
        
        const noteDuration = 60 / this.musicParams.bpm / 4; // Шістнадцяті ноти
        
        // Відтворення ноти
        this.playMusicNote(this.nextNoteTime);
        
        // Планування наступної ноти
        this.nextNoteTime += noteDuration;
        this.noteIndex++;
        
        // Зміна акорду кожні 16 нот
        if (this.noteIndex % 16 === 0) {
            this.musicParams.currentChord = 
                (this.musicParams.currentChord + 1) % this.musicParams.chordProgression.length;
        }
        
        // Планування наступного виклику
        setTimeout(() => this.scheduleNextNote(), (this.nextNoteTime - this.audioContext.currentTime) * 1000);
    }
    
    /**
     * Відтворення музичної ноти
     */
    playMusicNote(startTime) {
        if (!this.isMusicPlaying) return;
        
        // Вибір ноти з поточного акорду
        const chord = this.musicParams.chordProgression[this.musicParams.currentChord];
        const notes = this.getChordNotes(chord);
        const note = notes[Math.floor(Math.random() * notes.length)];
        const frequency = this.noteToFrequency(note);
        
        // Створення осцилятора
        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();
        const filter = this.audioContext.createBiquadFilter();
        
        osc.connect(filter);
        filter.connect(gain);
        gain.connect(this.musicGain);
        
        // Налаштування звуку
        osc.type = 'triangle';
        osc.frequency.value = frequency;
        
        filter.type = 'lowpass';
        filter.frequency.value = 2000;
        filter.Q.value = 1;
        
        // Envelope
        const duration = 0.5;
        gain.gain.setValueAtTime(0, startTime);
        gain.gain.linearRampToValueAtTime(0.1, startTime + 0.01);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + duration);
        
        osc.start(startTime);
        osc.stop(startTime + duration);
    }
    
    /**
     * Отримання нот акорду
     */
    getChordNotes(chord) {
        const chordMap = {
            'C': ['C4', 'E4', 'G4'],
            'Am': ['A4', 'C5', 'E5'],
            'F': ['F4', 'A4', 'C5'],
            'G': ['G4', 'B4', 'D5']
        };
        
        return chordMap[chord] || ['C4', 'E4', 'G4'];
    }
    
    /**
     * Конвертація ноти в частоту
     */
    noteToFrequency(note) {
        const noteMap = {
            'C4': 261.63, 'D4': 293.66, 'E4': 329.63, 'F4': 349.23,
            'G4': 392.00, 'A4': 440.00, 'B4': 493.88,
            'C5': 523.25, 'D5': 587.33, 'E5': 659.25, 'F5': 698.46,
            'G5': 783.99, 'A5': 880.00, 'B5': 987.77
        };
        
        return noteMap[note] || 440;
    }
    
    /**
     * Відтворення звукового ефекту
     */
    playSound(type, variant = null) {
        if (!this.isInitialized) {
            // Спроба ініціалізації при першому використанні
            this.initialize().then(() => {
                this.playSound(type, variant);
            });
            return;
        }

        if (this.sfxGenerator) {
            this.sfxGenerator.generateSFX(type, variant);
        } else {
            console.warn(`SFX генератор не ініціалізований`);
        }
    }
    
    /**
     * Пауза фонової музики
     */
    pauseBackgroundMusic() {
        this.isMusicPlaying = false;

        if (this.musicGenerator) {
            this.musicGenerator.stop();
        }

        console.log('Фонова музика поставлена на паузу');
    }
    
    /**
     * Відновлення фонової музики
     */
    resumeBackgroundMusic() {
        if (!this.isMusicPlaying) {
            this.playBackgroundMusic();
        }
    }
    
    /**
     * Зупинка всієї музики
     */
    stopBackgroundMusic() {
        this.pauseBackgroundMusic();
        console.log('Фонова музика зупинена');
    }

    /**
     * Адаптація музики до стану гри
     */
    adaptMusicToGameState(gameState) {
        if (this.musicGenerator) {
            this.musicGenerator.adaptToGameState(gameState);
        }
    }
    
    /**
     * Оновлення рівнів гучності
     */
    updateVolumes() {
        if (!this.isInitialized) return;
        
        this.masterGain.gain.value = this.volumes.master;
        this.musicGain.gain.value = this.volumes.music;
        this.sfxGain.gain.value = this.volumes.sfx;
    }
    
    /**
     * Встановлення гучності
     */
    setVolume(type, value) {
        this.volumes[type] = Math.max(0, Math.min(1, value));
        this.updateVolumes();
    }
    
    /**
     * Отримання поточної гучності
     */
    getVolume(type) {
        return this.volumes[type] || 0;
    }
    
    /**
     * Вимкнення/увімкнення звуку
     */
    toggleMute() {
        if (this.volumes.master > 0) {
            this.previousMasterVolume = this.volumes.master;
            this.setVolume('master', 0);
        } else {
            this.setVolume('master', this.previousMasterVolume || 0.7);
        }
    }
    
    /**
     * Перевірка чи звук вимкнено
     */
    isMuted() {
        return this.volumes.master === 0;
    }
    
    /**
     * Зміна музичного темпу
     */
    changeTempo(newBpm) {
        this.musicParams.bpm = Math.max(60, Math.min(180, newBpm));
        console.log(`Темп змінено на ${this.musicParams.bpm} BPM`);
    }
    
    /**
     * Зміна музичної тональності
     */
    changeKey(newKey) {
        const validKeys = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];
        if (validKeys.includes(newKey)) {
            this.musicParams.key = newKey;
            console.log(`Тональність змінена на ${newKey}`);
        }
    }
    
    /**
     * Очищення ресурсів
     */
    destroy() {
        this.stopBackgroundMusic();
        
        if (this.audioContext && this.audioContext.state !== 'closed') {
            this.audioContext.close();
        }
        
        this.sfxCache.clear();
        console.log('Аудіо система очищена');
    }
}
