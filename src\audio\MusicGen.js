/**
 * Клас MusicGen - процедурна генерація музики
 * Створює динамічну фонову музику на основі стану гри
 */

export class MusicGenerator {
    constructor(audioContext) {
        this.audioContext = audioContext;
        this.isPlaying = false;
        
        // Музичні параметри
        this.tempo = 120; // BPM
        this.key = 'C';
        this.mode = 'major';
        
        // Прогресії акордів для різних настроїв
        this.chordProgressions = {
            peaceful: ['I', 'vi', 'IV', 'V'],
            energetic: ['I', 'V', 'vi', 'IV'],
            mysterious: ['i', 'VII', 'VI', 'VII'],
            triumphant: ['I', 'IV', 'V', 'I']
        };
        
        this.currentProgression = this.chordProgressions.peaceful;
        this.currentChordIndex = 0;
        
        // Інструменти
        this.instruments = {
            lead: null,
            bass: null,
            pad: null,
            percussion: null
        };
        
        // Таймери
        this.nextNoteTime = 0;
        this.lookahead = 25.0; // мілісекунди
        this.scheduleAheadTime = 0.1; // секунди
        this.timerID = null;
    }
    
    /**
     * Ініціалізація генератора музики
     */
    async initialize() {
        console.log('Ініціалізація генератора музики...');
        
        // Створення інструментів
        await this.createInstruments();
        
        console.log('Генератор музики ініціалізований');
    }
    
    /**
     * Створення віртуальних інструментів
     */
    async createInstruments() {
        // Ведучий синтезатор
        this.instruments.lead = this.createLeadSynth();
        
        // Басовий синтезатор
        this.instruments.bass = this.createBassSynth();
        
        // Pad синтезатор для атмосфери
        this.instruments.pad = this.createPadSynth();
        
        // Перкусія
        this.instruments.percussion = this.createPercussion();
    }
    
    /**
     * Створення ведучого синтезатора
     */
    createLeadSynth() {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        const filter = this.audioContext.createBiquadFilter();
        
        oscillator.type = 'triangle';
        filter.type = 'lowpass';
        filter.frequency.value = 2000;
        filter.Q.value = 1;
        
        oscillator.connect(filter);
        filter.connect(gainNode);
        
        return {
            oscillator,
            gainNode,
            filter,
            play: (frequency, duration, startTime) => {
                const osc = this.audioContext.createOscillator();
                const gain = this.audioContext.createGain();
                const flt = this.audioContext.createBiquadFilter();
                
                osc.type = 'triangle';
                osc.frequency.value = frequency;
                
                flt.type = 'lowpass';
                flt.frequency.value = 2000;
                
                osc.connect(flt);
                flt.connect(gain);
                gain.connect(this.audioContext.destination);
                
                // ADSR envelope
                gain.gain.setValueAtTime(0, startTime);
                gain.gain.linearRampToValueAtTime(0.1, startTime + 0.01);
                gain.gain.exponentialRampToValueAtTime(0.05, startTime + duration * 0.3);
                gain.gain.exponentialRampToValueAtTime(0.01, startTime + duration);
                
                osc.start(startTime);
                osc.stop(startTime + duration);
            }
        };
    }
    
    /**
     * Створення басового синтезатора
     */
    createBassSynth() {
        return {
            play: (frequency, duration, startTime) => {
                const osc = this.audioContext.createOscillator();
                const gain = this.audioContext.createGain();
                
                osc.type = 'sawtooth';
                osc.frequency.value = frequency / 2; // Октава нижче
                
                osc.connect(gain);
                gain.connect(this.audioContext.destination);
                
                gain.gain.setValueAtTime(0, startTime);
                gain.gain.linearRampToValueAtTime(0.15, startTime + 0.01);
                gain.gain.exponentialRampToValueAtTime(0.01, startTime + duration);
                
                osc.start(startTime);
                osc.stop(startTime + duration);
            }
        };
    }
    
    /**
     * Створення pad синтезатора
     */
    createPadSynth() {
        return {
            play: (frequencies, duration, startTime) => {
                frequencies.forEach((freq, index) => {
                    const osc = this.audioContext.createOscillator();
                    const gain = this.audioContext.createGain();
                    const filter = this.audioContext.createBiquadFilter();
                    
                    osc.type = 'sine';
                    osc.frequency.value = freq;
                    
                    filter.type = 'lowpass';
                    filter.frequency.value = 800;
                    
                    osc.connect(filter);
                    filter.connect(gain);
                    gain.connect(this.audioContext.destination);
                    
                    // Повільний attack для pad звуку
                    gain.gain.setValueAtTime(0, startTime);
                    gain.gain.linearRampToValueAtTime(0.03, startTime + 0.5);
                    gain.gain.linearRampToValueAtTime(0.02, startTime + duration - 0.5);
                    gain.gain.exponentialRampToValueAtTime(0.01, startTime + duration);
                    
                    osc.start(startTime);
                    osc.stop(startTime + duration);
                });
            }
        };
    }
    
    /**
     * Створення перкусії
     */
    createPercussion() {
        return {
            playKick: (startTime) => {
                const osc = this.audioContext.createOscillator();
                const gain = this.audioContext.createGain();
                
                osc.type = 'sine';
                osc.frequency.setValueAtTime(60, startTime);
                osc.frequency.exponentialRampToValueAtTime(30, startTime + 0.1);
                
                osc.connect(gain);
                gain.connect(this.audioContext.destination);
                
                gain.gain.setValueAtTime(0.3, startTime);
                gain.gain.exponentialRampToValueAtTime(0.01, startTime + 0.2);
                
                osc.start(startTime);
                osc.stop(startTime + 0.2);
            },
            
            playHiHat: (startTime) => {
                const bufferSize = this.audioContext.sampleRate * 0.1;
                const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
                const data = buffer.getChannelData(0);
                
                // Генерація білого шуму
                for (let i = 0; i < bufferSize; i++) {
                    data[i] = Math.random() * 2 - 1;
                }
                
                const source = this.audioContext.createBufferSource();
                const gain = this.audioContext.createGain();
                const filter = this.audioContext.createBiquadFilter();
                
                source.buffer = buffer;
                filter.type = 'highpass';
                filter.frequency.value = 8000;
                
                source.connect(filter);
                filter.connect(gain);
                gain.connect(this.audioContext.destination);
                
                gain.gain.setValueAtTime(0.1, startTime);
                gain.gain.exponentialRampToValueAtTime(0.01, startTime + 0.1);
                
                source.start(startTime);
            }
        };
    }
    
    /**
     * Запуск генерації музики
     */
    start() {
        if (this.isPlaying) return;
        
        this.isPlaying = true;
        this.nextNoteTime = this.audioContext.currentTime;
        this.scheduler();
        
        console.log('Генерація музики запущена');
    }
    
    /**
     * Зупинка генерації музики
     */
    stop() {
        this.isPlaying = false;
        
        if (this.timerID) {
            clearTimeout(this.timerID);
            this.timerID = null;
        }
        
        console.log('Генерація музики зупинена');
    }
    
    /**
     * Планувальник нот
     */
    scheduler() {
        while (this.nextNoteTime < this.audioContext.currentTime + this.scheduleAheadTime) {
            this.scheduleNote(this.nextNoteTime);
            this.nextNote();
        }
        
        if (this.isPlaying) {
            this.timerID = setTimeout(() => this.scheduler(), this.lookahead);
        }
    }
    
    /**
     * Планування наступної ноти
     */
    scheduleNote(time) {
        // Отримання поточного акорду
        const chord = this.getCurrentChord();
        const chordNotes = this.getChordNotes(chord);
        
        // Відтворення басової ноти
        if (this.shouldPlayBass()) {
            this.instruments.bass.play(chordNotes[0], 0.5, time);
        }
        
        // Відтворення мелодії
        if (this.shouldPlayMelody()) {
            const melodyNote = this.generateMelodyNote(chordNotes);
            this.instruments.lead.play(melodyNote, 0.3, time);
        }
        
        // Відтворення pad акордів
        if (this.shouldPlayPad()) {
            this.instruments.pad.play(chordNotes, 2.0, time);
        }
        
        // Відтворення перкусії
        if (this.shouldPlayPercussion()) {
            this.instruments.percussion.playKick(time);
        }
    }
    
    /**
     * Перехід до наступної ноти
     */
    nextNote() {
        const secondsPerBeat = 60.0 / this.tempo;
        this.nextNoteTime += secondsPerBeat / 4; // Шістнадцяті ноти
    }
    
    /**
     * Отримання поточного акорду
     */
    getCurrentChord() {
        return this.currentProgression[this.currentChordIndex];
    }
    
    /**
     * Отримання нот акорду
     */
    getChordNotes(chord) {
        const rootNote = this.noteToFrequency(this.key + '4');
        
        // Спрощена система акордів
        const intervals = {
            'I': [0, 4, 7],      // Мажорний тризвук
            'ii': [2, 5, 9],     // Мінорний тризвук
            'iii': [4, 7, 11],   // Мінорний тризвук
            'IV': [5, 9, 0],     // Мажорний тризвук
            'V': [7, 11, 2],     // Мажорний тризвук
            'vi': [9, 0, 4],     // Мінорний тризвук
            'vii': [11, 2, 5]    // Зменшений тризвук
        };
        
        const chordIntervals = intervals[chord] || intervals['I'];
        
        return chordIntervals.map(interval => {
            const semitones = interval;
            return rootNote * Math.pow(2, semitones / 12);
        });
    }
    
    /**
     * Конвертація ноти в частоту
     */
    noteToFrequency(note) {
        const noteMap = {
            'C4': 261.63, 'D4': 293.66, 'E4': 329.63, 'F4': 349.23,
            'G4': 392.00, 'A4': 440.00, 'B4': 493.88,
            'C5': 523.25, 'D5': 587.33, 'E5': 659.25
        };
        
        return noteMap[note] || 440;
    }
    
    /**
     * Генерація мелодійної ноти
     */
    generateMelodyNote(chordNotes) {
        // Вибір ноти з акорду з додаванням проходящих нот
        const extendedNotes = [...chordNotes];
        
        // Додавання проходящих нот (сусідні ноти)
        chordNotes.forEach(note => {
            extendedNotes.push(note * Math.pow(2, 1/12)); // +1 семітон
            extendedNotes.push(note * Math.pow(2, -1/12)); // -1 семітон
        });
        
        return extendedNotes[Math.floor(Math.random() * extendedNotes.length)];
    }
    
    /**
     * Логіка відтворення басу
     */
    shouldPlayBass() {
        // Бас грає на сильні долі
        const beatPosition = (this.audioContext.currentTime * this.tempo / 60) % 4;
        return beatPosition < 0.1 || Math.abs(beatPosition - 2) < 0.1;
    }
    
    /**
     * Логіка відтворення мелодії
     */
    shouldPlayMelody() {
        // Мелодія грає випадково з певною ймовірністю
        return Math.random() < 0.3;
    }
    
    /**
     * Логіка відтворення pad
     */
    shouldPlayPad() {
        // Pad грає на початку кожного такту
        const beatPosition = (this.audioContext.currentTime * this.tempo / 60) % 16;
        return beatPosition < 0.1;
    }
    
    /**
     * Логіка відтворення перкусії
     */
    shouldPlayPercussion() {
        // Перкусія грає на кожну четверть
        const beatPosition = (this.audioContext.currentTime * this.tempo / 60) % 1;
        return beatPosition < 0.05;
    }
    
    /**
     * Зміна настрою музики
     */
    changeMood(mood) {
        if (this.chordProgressions[mood]) {
            this.currentProgression = this.chordProgressions[mood];
            this.currentChordIndex = 0;
            console.log(`Настрій музики змінено на: ${mood}`);
        }
    }
    
    /**
     * Зміна темпу
     */
    changeTempo(newTempo) {
        this.tempo = Math.max(60, Math.min(180, newTempo));
        console.log(`Темп змінено на: ${this.tempo} BPM`);
    }
    
    /**
     * Зміна тональності
     */
    changeKey(newKey) {
        const validKeys = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];
        if (validKeys.includes(newKey)) {
            this.key = newKey;
            console.log(`Тональність змінена на: ${newKey}`);
        }
    }
    
    /**
     * Адаптація музики до стану гри
     */
    adaptToGameState(gameState) {
        switch (gameState.level) {
            case 1:
                this.changeMood('peaceful');
                this.changeTempo(100);
                break;
            case 2:
            case 3:
                this.changeMood('energetic');
                this.changeTempo(120);
                break;
            default:
                if (gameState.level >= 10) {
                    this.changeMood('triumphant');
                    this.changeTempo(140);
                } else {
                    this.changeMood('mysterious');
                    this.changeTempo(110);
                }
        }
        
        // Адаптація до кількості ходів
        if (gameState.moves <= 5) {
            this.changeTempo(this.tempo + 20); // Прискорення при малій кількості ходів
        }
    }
    
    /**
     * Створення динамічного саундтреку
     */
    createDynamicSoundtrack(duration = 60000) {
        const sections = [
            { mood: 'peaceful', duration: 0.3 },
            { mood: 'energetic', duration: 0.4 },
            { mood: 'mysterious', duration: 0.2 },
            { mood: 'triumphant', duration: 0.1 }
        ];
        
        let currentTime = 0;
        
        sections.forEach(section => {
            const sectionDuration = duration * section.duration;
            
            setTimeout(() => {
                this.changeMood(section.mood);
            }, currentTime);
            
            currentTime += sectionDuration;
        });
        
        // Повернення до початкового настрою
        setTimeout(() => {
            this.changeMood('peaceful');
        }, duration);
    }
}
