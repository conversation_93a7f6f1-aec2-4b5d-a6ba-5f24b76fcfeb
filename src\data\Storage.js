/**
 * Клас Storage - управління збереженням даних гри
 * Використовує LocalStorage та IndexedDB для різних типів даних
 */

export class Storage {
    constructor() {
        this.db = null;
        this.dbName = 'ChemicalElementsGame';
        this.dbVersion = 1;
        
        // Ключі для LocalStorage
        this.storageKeys = {
            settings: 'game_settings',
            quickSave: 'game_quick_save',
            preferences: 'user_preferences'
        };
        
        // Схема бази даних
        this.dbSchema = {
            gameStates: '++id, timestamp, level, score, moves',
            achievements: '++id, type, timestamp, data',
            statistics: '++id, date, gamesPlayed, totalScore, averageScore',
            leaderboard: '++id, playerName, score, level, timestamp'
        };
    }
    
    /**
     * Ініціалізація системи збереження
     */
    async initialize() {
        console.log('Ініціалізація системи збереження...');
        
        try {
            // Ініціалізація IndexedDB
            await this.initializeIndexedDB();
            
            // Перевірка підтримки LocalStorage
            this.checkLocalStorageSupport();
            
            // Міграція старих даних якщо необхідно
            await this.migrateOldData();
            
            console.log('Система збереження ініціалізована');
            
        } catch (error) {
            console.error('Помилка ініціалізації системи збереження:', error);
            // Fallback на LocalStorage
            console.log('Використання тільки LocalStorage');
        }
    }
    
    /**
     * Ініціалізація IndexedDB
     */
    async initializeIndexedDB() {
        if (typeof Dexie === 'undefined') {
            throw new Error('Dexie не доступний');
        }
        
        this.db = new Dexie(this.dbName);
        this.db.version(this.dbVersion).stores(this.dbSchema);
        
        await this.db.open();
        console.log('IndexedDB ініціалізована');
    }
    
    /**
     * Перевірка підтримки LocalStorage
     */
    checkLocalStorageSupport() {
        try {
            const testKey = '__storage_test__';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            console.log('LocalStorage підтримується');
        } catch (error) {
            console.warn('LocalStorage не підтримується:', error);
        }
    }
    
    /**
     * Збереження стану гри
     */
    async saveGameState(gameState) {
        try {
            // Додавання контрольної суми
            gameState.checksum = this.calculateChecksum(gameState);
            
            // Збереження в IndexedDB
            if (this.db) {
                await this.db.gameStates.add(gameState);
                
                // Очищення старих збережень (залишаємо тільки останні 10)
                const allStates = await this.db.gameStates.orderBy('timestamp').toArray();
                if (allStates.length > 10) {
                    const toDelete = allStates.slice(0, allStates.length - 10);
                    for (const state of toDelete) {
                        await this.db.gameStates.delete(state.id);
                    }
                }
            }
            
            // Швидке збереження в LocalStorage
            localStorage.setItem(this.storageKeys.quickSave, JSON.stringify(gameState));
            
            console.log('Стан гри збережено');
            
        } catch (error) {
            console.error('Помилка збереження стану гри:', error);
        }
    }
    
    /**
     * Завантаження стану гри
     */
    async getGameState() {
        try {
            let gameState = null;
            
            // Спроба завантаження з IndexedDB
            if (this.db) {
                const states = await this.db.gameStates.orderBy('timestamp').reverse().limit(1).toArray();
                if (states.length > 0) {
                    gameState = states[0];
                }
            }
            
            // Fallback на LocalStorage
            if (!gameState) {
                const quickSave = localStorage.getItem(this.storageKeys.quickSave);
                if (quickSave) {
                    gameState = JSON.parse(quickSave);
                }
            }
            
            // Валідація контрольної суми
            if (gameState && !this.validateChecksum(gameState)) {
                console.warn('Контрольна сума не співпадає, ігноруємо збережений стан');
                return null;
            }
            
            return gameState;
            
        } catch (error) {
            console.error('Помилка завантаження стану гри:', error);
            return null;
        }
    }
    
    /**
     * Збереження налаштувань
     */
    async saveSettings(settings) {
        try {
            const settingsData = {
                ...settings,
                timestamp: Date.now(),
                version: this.dbVersion
            };
            
            localStorage.setItem(this.storageKeys.settings, JSON.stringify(settingsData));
            console.log('Налаштування збережено');
            
        } catch (error) {
            console.error('Помилка збереження налаштувань:', error);
        }
    }
    
    /**
     * Завантаження налаштувань
     */
    async getSettings() {
        try {
            const settingsData = localStorage.getItem(this.storageKeys.settings);
            if (settingsData) {
                const settings = JSON.parse(settingsData);
                
                // Перевірка версії
                if (settings.version === this.dbVersion) {
                    return settings;
                } else {
                    console.log('Налаштування застарілі, використовуємо стандартні');
                }
            }
            
            return null;
            
        } catch (error) {
            console.error('Помилка завантаження налаштувань:', error);
            return null;
        }
    }
    
    /**
     * Збереження досягнення
     */
    async saveAchievement(type, data) {
        try {
            const achievement = {
                type: type,
                data: data,
                timestamp: Date.now()
            };
            
            if (this.db) {
                await this.db.achievements.add(achievement);
                console.log(`Досягнення збережено: ${type}`);
            }
            
        } catch (error) {
            console.error('Помилка збереження досягнення:', error);
        }
    }
    
    /**
     * Отримання всіх досягнень
     */
    async getAchievements() {
        try {
            if (this.db) {
                return await this.db.achievements.orderBy('timestamp').toArray();
            }
            return [];
            
        } catch (error) {
            console.error('Помилка завантаження досягнень:', error);
            return [];
        }
    }
    
    /**
     * Збереження статистики
     */
    async saveStatistics(stats) {
        try {
            const today = new Date().toISOString().split('T')[0];
            
            if (this.db) {
                // Перевірка чи є запис за сьогодні
                const existingStats = await this.db.statistics.where('date').equals(today).first();
                
                if (existingStats) {
                    // Оновлення існуючого запису
                    await this.db.statistics.update(existingStats.id, {
                        gamesPlayed: existingStats.gamesPlayed + 1,
                        totalScore: existingStats.totalScore + stats.score,
                        averageScore: (existingStats.totalScore + stats.score) / (existingStats.gamesPlayed + 1)
                    });
                } else {
                    // Створення нового запису
                    await this.db.statistics.add({
                        date: today,
                        gamesPlayed: 1,
                        totalScore: stats.score,
                        averageScore: stats.score
                    });
                }
            }
            
        } catch (error) {
            console.error('Помилка збереження статистики:', error);
        }
    }
    
    /**
     * Отримання статистики
     */
    async getStatistics(days = 30) {
        try {
            if (this.db) {
                const cutoffDate = new Date();
                cutoffDate.setDate(cutoffDate.getDate() - days);
                const cutoffString = cutoffDate.toISOString().split('T')[0];
                
                return await this.db.statistics
                    .where('date')
                    .above(cutoffString)
                    .orderBy('date')
                    .toArray();
            }
            return [];
            
        } catch (error) {
            console.error('Помилка завантаження статистики:', error);
            return [];
        }
    }
    
    /**
     * Очищення старих даних
     */
    async cleanupOldData() {
        try {
            const cutoffDate = Date.now() - (30 * 24 * 60 * 60 * 1000); // 30 днів
            
            if (this.db) {
                // Очищення старих станів гри
                await this.db.gameStates.where('timestamp').below(cutoffDate).delete();
                
                // Очищення старих досягнень
                await this.db.achievements.where('timestamp').below(cutoffDate).delete();
                
                console.log('Старі дані очищено');
            }
            
        } catch (error) {
            console.error('Помилка очищення старих даних:', error);
        }
    }
    
    /**
     * Розрахунок контрольної суми
     */
    calculateChecksum(data) {
        const str = JSON.stringify(data, Object.keys(data).sort());
        let hash = 0;
        
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Конвертація в 32-бітне число
        }
        
        return hash.toString(36);
    }
    
    /**
     * Валідація контрольної суми
     */
    validateChecksum(data) {
        if (!data.checksum) return false;
        
        const originalChecksum = data.checksum;
        delete data.checksum;
        
        const calculatedChecksum = this.calculateChecksum(data);
        data.checksum = originalChecksum;
        
        return originalChecksum === calculatedChecksum;
    }
    
    /**
     * Міграція старих даних
     */
    async migrateOldData() {
        // Перевірка на наявність старих даних в LocalStorage
        const oldKeys = ['gameData', 'userProgress', 'gameSettings'];
        
        for (const key of oldKeys) {
            const oldData = localStorage.getItem(key);
            if (oldData) {
                console.log(`Знайдено старі дані: ${key}`);
                // Тут можна додати логіку міграції
                localStorage.removeItem(key);
            }
        }
    }
    
    /**
     * Експорт даних гри
     */
    async exportGameData() {
        try {
            const exportData = {
                settings: await this.getSettings(),
                gameState: await this.getGameState(),
                achievements: await this.getAchievements(),
                statistics: await this.getStatistics(),
                exportDate: new Date().toISOString(),
                version: this.dbVersion
            };
            
            return JSON.stringify(exportData, null, 2);
            
        } catch (error) {
            console.error('Помилка експорту даних:', error);
            return null;
        }
    }
    
    /**
     * Імпорт даних гри
     */
    async importGameData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            
            // Валідація версії
            if (data.version !== this.dbVersion) {
                console.warn('Версія даних не співпадає');
            }
            
            // Відновлення налаштувань
            if (data.settings) {
                await this.saveSettings(data.settings);
            }
            
            // Відновлення стану гри
            if (data.gameState) {
                await this.saveGameState(data.gameState);
            }
            
            console.log('Дані успішно імпортовано');
            return true;
            
        } catch (error) {
            console.error('Помилка імпорту даних:', error);
            return false;
        }
    }
    
    /**
     * Очищення всіх даних
     */
    async clearAllData() {
        try {
            // Очищення LocalStorage
            Object.values(this.storageKeys).forEach(key => {
                localStorage.removeItem(key);
            });
            
            // Очищення IndexedDB
            if (this.db) {
                await this.db.delete();
                await this.initializeIndexedDB();
            }
            
            console.log('Всі дані очищено');
            
        } catch (error) {
            console.error('Помилка очищення даних:', error);
        }
    }
    
    /**
     * Отримання розміру використаного сховища
     */
    async getStorageUsage() {
        let usage = {
            localStorage: 0,
            indexedDB: 0,
            total: 0
        };
        
        try {
            // Розрахунок розміру LocalStorage
            let localStorageSize = 0;
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    localStorageSize += localStorage[key].length + key.length;
                }
            }
            usage.localStorage = localStorageSize;
            
            // Розрахунок розміру IndexedDB (приблизний)
            if (this.db) {
                const tables = ['gameStates', 'achievements', 'statistics', 'leaderboard'];
                let indexedDBSize = 0;
                
                for (const table of tables) {
                    const count = await this.db[table].count();
                    indexedDBSize += count * 1000; // Приблизна оцінка
                }
                
                usage.indexedDB = indexedDBSize;
            }
            
            usage.total = usage.localStorage + usage.indexedDB;
            
        } catch (error) {
            console.error('Помилка розрахунку використання сховища:', error);
        }
        
        return usage;
    }
}
