/**
 * К<PERSON><PERSON><PERSON> Renderer - система рендерингу гри
 * Відповідає за відображення ігрової сітки, елементів та ефектів
 */

import { ParticleSystem } from './Particles.js';

export class Renderer {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.width = 0;
        this.height = 0;
        this.cellSize = 0;
        this.gridOffsetX = 0;
        this.gridOffsetY = 0;
        
        // Кольорові теми
        this.themes = {
            default: {
                background: '#ecf0f1',
                gridLines: '#bdc3c7',
                selectedCell: '#f39c12',
                highlightCell: '#e67e22'
            },
            dark: {
                background: '#2c3e50',
                gridLines: '#34495e',
                selectedCell: '#f39c12',
                highlightCell: '#e67e22'
            }
        };
        
        this.currentTheme = this.themes.default;
        
        // Система частинок
        this.particleSystem = null;

        // Кеш для оптимізації
        this.elementCache = new Map();
        this.lastFrameTime = 0;
    }
    
    /**
     * Ініціалізація системи рендерингу
     */
    async initialize() {
        console.log('Ініціалізація системи рендерингу...');
        
        // Отримання canvas елемента
        this.canvas = document.getElementById('gameCanvas');
        if (!this.canvas) {
            throw new Error('Canvas елемент не знайдено');
        }
        
        this.ctx = this.canvas.getContext('2d');
        if (!this.ctx) {
            throw new Error('Не вдалося отримати 2D контекст canvas');
        }
        
        // Налаштування canvas для високої роздільності
        this.setupHighDPI();
        
        // Початкове налаштування розмірів
        this.handleResize();
        
        // Ініціалізація кешу елементів
        this.initializeElementCache();

        // Ініціалізація системи частинок
        this.particleSystem = new ParticleSystem(this);

        console.log('Система рендерингу ініціалізована');
    }
    
    /**
     * Налаштування високої роздільності для Retina дисплеїв
     */
    setupHighDPI() {
        const dpr = window.devicePixelRatio || 1;
        const rect = this.canvas.getBoundingClientRect();
        
        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;
        
        this.ctx.scale(dpr, dpr);
        
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
    }
    
    /**
     * Обробка зміни розміру
     */
    handleResize() {
        const container = document.getElementById('gameBoard');
        const containerRect = container.getBoundingClientRect();
        
        // Розрахунок оптимального розміру canvas
        const maxSize = Math.min(containerRect.width - 40, containerRect.height - 40);
        const canvasSize = Math.floor(maxSize);
        
        this.canvas.style.width = canvasSize + 'px';
        this.canvas.style.height = canvasSize + 'px';
        
        this.width = canvasSize;
        this.height = canvasSize;
        
        // Перерахунок розмірів клітинок
        this.calculateCellSize();
        
        // Оновлення canvas розмірів
        this.setupHighDPI();
        
        console.log(`Розмір canvas оновлено: ${canvasSize}x${canvasSize}`);
    }
    
    /**
     * Розрахунок розміру клітинок
     */
    calculateCellSize() {
        const gridSize = 8; // Стандартний розмір сітки
        const padding = 20;
        
        this.cellSize = Math.floor((Math.min(this.width, this.height) - padding * 2) / gridSize);
        
        // Центрування сітки
        const totalGridSize = this.cellSize * gridSize;
        this.gridOffsetX = (this.width - totalGridSize) / 2;
        this.gridOffsetY = (this.height - totalGridSize) / 2;
    }
    
    /**
     * Ініціалізація кешу елементів
     */
    initializeElementCache() {
        // Очищення попереднього кешу
        this.elementCache.clear();
        
        // Попереднє створення зображень елементів
        const elements = [
            { id: 0, color: '#e74c3c', symbol: '🔥' },
            { id: 1, color: '#3498db', symbol: '💧' },
            { id: 2, color: '#8b4513', symbol: '🌍' },
            { id: 3, color: '#ecf0f1', symbol: '💨' },
            { id: 4, color: '#f39c12', symbol: '⚡' },
            { id: 5, color: '#9b59b6', symbol: '💎' },
            { id: 6, color: '#bdc3c7', symbol: '☁️' },
            { id: 7, color: '#e67e22', symbol: '🌋' }
        ];
        
        elements.forEach(element => {
            this.cacheElement(element);
        });
    }
    
    /**
     * Кешування зображення елемента
     */
    cacheElement(element) {
        const cacheCanvas = document.createElement('canvas');
        const cacheCtx = cacheCanvas.getContext('2d');
        
        cacheCanvas.width = this.cellSize;
        cacheCanvas.height = this.cellSize;
        
        // Малювання елемента
        this.drawElementToCache(cacheCtx, element, this.cellSize);
        
        this.elementCache.set(element.id, cacheCanvas);
    }
    
    /**
     * Малювання елемента в кеш
     */
    drawElementToCache(ctx, element, size) {
        const centerX = size / 2;
        const centerY = size / 2;
        const radius = size * 0.4;
        
        // Очищення
        ctx.clearRect(0, 0, size, size);
        
        // Тінь
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        ctx.shadowBlur = 4;
        ctx.shadowOffsetX = 2;
        ctx.shadowOffsetY = 2;
        
        // Основне коло елемента
        const gradient = ctx.createRadialGradient(
            centerX - radius * 0.3, centerY - radius * 0.3, 0,
            centerX, centerY, radius
        );
        
        const color = tinycolor(element.color);
        gradient.addColorStop(0, color.lighten(20).toHexString());
        gradient.addColorStop(0.7, element.color);
        gradient.addColorStop(1, color.darken(20).toHexString());
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
        ctx.fill();
        
        // Скидання тіні
        ctx.shadowColor = 'transparent';
        
        // Внутрішнє світло
        ctx.fillStyle = color.lighten(40).setAlpha(0.6).toRgbaString();
        ctx.beginPath();
        ctx.arc(centerX - radius * 0.2, centerY - radius * 0.2, radius * 0.3, 0, Math.PI * 2);
        ctx.fill();
        
        // Символ елемента
        ctx.fillStyle = '#ffffff';
        ctx.font = `${size * 0.3}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(element.symbol, centerX, centerY);
        
        // Обводка
        ctx.strokeStyle = color.darken(30).toHexString();
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
        ctx.stroke();
    }
    
    /**
     * Очищення canvas
     */
    clear() {
        this.ctx.clearRect(0, 0, this.width, this.height);
        
        // Малювання фону
        const gradient = this.ctx.createLinearGradient(0, 0, this.width, this.height);
        gradient.addColorStop(0, '#667eea');
        gradient.addColorStop(1, '#764ba2');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.width, this.height);
    }
    
    /**
     * Рендеринг ігрової сітки
     */
    renderGrid(grid) {
        if (!grid || !grid.cells) return;
        
        // Малювання фону сітки
        this.drawGridBackground();
        
        // Малювання клітинок та елементів
        for (let x = 0; x < grid.size; x++) {
            for (let y = 0; y < grid.size; y++) {
                const cell = grid.cells[x][y];
                const screenX = this.gridOffsetX + x * this.cellSize;
                const screenY = this.gridOffsetY + y * this.cellSize;
                
                // Малювання клітинки
                this.drawCell(screenX, screenY, cell);
                
                // Малювання елемента
                if (cell.element) {
                    this.drawElement(screenX, screenY, cell);
                }
            }
        }
        
        // Малювання індикатора гравітації
        this.drawGravityIndicator(grid.gravityDirection);
    }
    
    /**
     * Малювання фону сітки
     */
    drawGridBackground() {
        const gridSize = 8;
        
        // Фон сітки
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        this.ctx.fillRect(
            this.gridOffsetX - 5, 
            this.gridOffsetY - 5,
            gridSize * this.cellSize + 10,
            gridSize * this.cellSize + 10
        );
        
        // Лінії сітки
        this.ctx.strokeStyle = this.currentTheme.gridLines;
        this.ctx.lineWidth = 1;
        
        for (let i = 0; i <= gridSize; i++) {
            // Вертикальні лінії
            const x = this.gridOffsetX + i * this.cellSize;
            this.ctx.beginPath();
            this.ctx.moveTo(x, this.gridOffsetY);
            this.ctx.lineTo(x, this.gridOffsetY + gridSize * this.cellSize);
            this.ctx.stroke();
            
            // Горизонтальні лінії
            const y = this.gridOffsetY + i * this.cellSize;
            this.ctx.beginPath();
            this.ctx.moveTo(this.gridOffsetX, y);
            this.ctx.lineTo(this.gridOffsetX + gridSize * this.cellSize, y);
            this.ctx.stroke();
        }
    }
    
    /**
     * Малювання окремої клітинки
     */
    drawCell(x, y, cell) {
        // Фон клітинки
        if (cell.isSelected) {
            this.ctx.fillStyle = this.currentTheme.selectedCell;
            this.ctx.fillRect(x + 2, y + 2, this.cellSize - 4, this.cellSize - 4);
        } else if (cell.isHighlighted) {
            this.ctx.fillStyle = this.currentTheme.highlightCell;
            this.ctx.fillRect(x + 2, y + 2, this.cellSize - 4, this.cellSize - 4);
        }
    }
    
    /**
     * Малювання елемента
     */
    drawElement(x, y, cell) {
        const element = cell.element;
        if (!element) return;
        
        this.ctx.save();
        
        // Застосування анімацій
        if (cell.isAnimating) {
            this.applyAnimation(x, y, cell);
        }
        
        // Використання кешованого зображення або малювання нового
        const cachedElement = this.elementCache.get(element.id);
        if (cachedElement) {
            this.ctx.drawImage(cachedElement, x + 2, y + 2, this.cellSize - 4, this.cellSize - 4);
        } else {
            this.drawElementDirect(x + 2, y + 2, this.cellSize - 4, element);
        }
        
        this.ctx.restore();
    }
    
    /**
     * Застосування анімацій до елемента
     */
    applyAnimation(x, y, cell) {
        const progress = cell.animationProgress;
        const centerX = x + this.cellSize / 2;
        const centerY = y + this.cellSize / 2;
        
        this.ctx.translate(centerX, centerY);
        
        switch (cell.animationType) {
            case 'appear':
                const scale = this.easeOutBack(progress);
                this.ctx.scale(scale, scale);
                break;
                
            case 'disappear':
                const fadeScale = 1 - this.easeInQuad(progress);
                this.ctx.scale(fadeScale, fadeScale);
                this.ctx.globalAlpha = 1 - progress;
                break;
                
            case 'fall':
                const fallY = -this.cellSize * (1 - this.easeOutBounce(progress));
                this.ctx.translate(0, fallY);
                break;
                
            case 'rise':
                const riseY = this.cellSize * (1 - this.easeOutBounce(progress));
                this.ctx.translate(0, riseY);
                break;
                
            case 'slideLeft':
                const slideLeftX = this.cellSize * (1 - this.easeOutQuad(progress));
                this.ctx.translate(slideLeftX, 0);
                break;
                
            case 'slideRight':
                const slideRightX = -this.cellSize * (1 - this.easeOutQuad(progress));
                this.ctx.translate(slideRightX, 0);
                break;
                
            case 'reaction':
                const reactionScale = 1 + Math.sin(progress * Math.PI * 4) * 0.2;
                this.ctx.scale(reactionScale, reactionScale);
                
                // Ефект світіння
                this.ctx.shadowColor = cell.element.color;
                this.ctx.shadowBlur = 20 * progress;
                break;
        }
        
        this.ctx.translate(-centerX, -centerY);
    }
    
    /**
     * Пряме малювання елемента без кешу
     */
    drawElementDirect(x, y, size, element) {
        const centerX = x + size / 2;
        const centerY = y + size / 2;
        const radius = size * 0.4;
        
        // Градієнт
        const gradient = this.ctx.createRadialGradient(
            centerX - radius * 0.3, centerY - radius * 0.3, 0,
            centerX, centerY, radius
        );
        
        const color = tinycolor(element.color);
        gradient.addColorStop(0, color.lighten(20).toHexString());
        gradient.addColorStop(0.7, element.color);
        gradient.addColorStop(1, color.darken(20).toHexString());
        
        // Основне коло
        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Символ
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = `${size * 0.3}px Arial`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(element.symbol, centerX, centerY);
    }
    
    /**
     * Малювання індикатора напрямку гравітації
     */
    drawGravityIndicator(direction) {
        const indicatorSize = 30;
        const margin = 10;
        
        let x, y, rotation;
        
        switch (direction) {
            case 'down':
                x = this.width - indicatorSize - margin;
                y = this.height - indicatorSize - margin;
                rotation = 0;
                break;
            case 'up':
                x = this.width - indicatorSize - margin;
                y = margin;
                rotation = Math.PI;
                break;
            case 'left':
                x = margin;
                y = this.height - indicatorSize - margin;
                rotation = Math.PI / 2;
                break;
            case 'right':
                x = this.width - indicatorSize - margin;
                y = this.height - indicatorSize - margin;
                rotation = -Math.PI / 2;
                break;
        }
        
        this.ctx.save();
        this.ctx.translate(x + indicatorSize / 2, y + indicatorSize / 2);
        this.ctx.rotate(rotation);
        
        // Фон індикатора
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        this.ctx.fillRect(-indicatorSize / 2, -indicatorSize / 2, indicatorSize, indicatorSize);
        
        // Стрілка
        this.ctx.fillStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.moveTo(0, -indicatorSize / 3);
        this.ctx.lineTo(-indicatorSize / 4, indicatorSize / 6);
        this.ctx.lineTo(indicatorSize / 4, indicatorSize / 6);
        this.ctx.closePath();
        this.ctx.fill();
        
        this.ctx.restore();
    }
    
    /**
     * Рендеринг ефектів та частинок
     */
    renderEffects() {
        const currentTime = performance.now();
        const deltaTime = currentTime - this.lastFrameTime;

        // Оновлення та рендеринг системи частинок
        if (this.particleSystem) {
            this.particleSystem.update(deltaTime);
            this.particleSystem.render(this.ctx);
        }

        this.lastFrameTime = currentTime;
    }
    
    /**
     * Додавання ефекту частинок
     */
    addParticleEffect(x, y, type, color, intensity = 1) {
        const screenX = this.gridOffsetX + x * this.cellSize + this.cellSize / 2;
        const screenY = this.gridOffsetY + y * this.cellSize + this.cellSize / 2;

        if (this.particleSystem) {
            this.particleSystem.createEffect(type, screenX, screenY, color, intensity);
        }
    }
    
    /**
     * Завершення рендерингу кадру
     */
    present() {
        // Додаткові пост-ефекти можуть бути додані тут
    }
    
    /**
     * Функції згладжування для анімацій
     */
    easeOutBack(t) {
        const c1 = 1.70158;
        const c3 = c1 + 1;
        return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2);
    }
    
    easeInQuad(t) {
        return t * t;
    }
    
    easeOutQuad(t) {
        return 1 - (1 - t) * (1 - t);
    }
    
    easeOutBounce(t) {
        const n1 = 7.5625;
        const d1 = 2.75;
        
        if (t < 1 / d1) {
            return n1 * t * t;
        } else if (t < 2 / d1) {
            return n1 * (t -= 1.5 / d1) * t + 0.75;
        } else if (t < 2.5 / d1) {
            return n1 * (t -= 2.25 / d1) * t + 0.9375;
        } else {
            return n1 * (t -= 2.625 / d1) * t + 0.984375;
        }
    }
    
    /**
     * Конвертація координат екрану в координати сітки
     */
    screenToGrid(screenX, screenY) {
        const gridX = Math.floor((screenX - this.gridOffsetX) / this.cellSize);
        const gridY = Math.floor((screenY - this.gridOffsetY) / this.cellSize);
        
        return { x: gridX, y: gridY };
    }
    
    /**
     * Конвертація координат сітки в координати екрану
     */
    gridToScreen(gridX, gridY) {
        const screenX = this.gridOffsetX + gridX * this.cellSize;
        const screenY = this.gridOffsetY + gridY * this.cellSize;
        
        return { x: screenX, y: screenY };
    }
}


