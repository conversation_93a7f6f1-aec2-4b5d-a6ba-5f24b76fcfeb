# Хімічні Елементи - Інноваційна HTML5 Гра-головоломка

Сучасна реалізація гри "три в ряд" з унікальними механіками хімії елементів та динамічної гравітації.

## 🎮 Особливості гри

### Унікальні механіки
- **Система хімії елементів**: Елементи реагують між собою, створюючи нові сполуки
- **Динамічна гравітація**: Напрямок падіння елементів змінюється кожні 45 секунд
- **Каскадні реакції**: Ланцюгові збіги та хімічні перетворення
- **Еволюція елементів**: Нові типи елементів з'являються на вищих рівнях

### Хімічні реакції
- 🔥 + 💧 = ☁️ (Вогонь + Вода = Пара)
- 🔥 + 🌍 = 🌋 (Вогонь + Земля = Лава)
- 💧 + 🌍 = 🌱 (Вода + Земля = Грязь)
- ⚡ + 💧 = ⛈️ (Енергія + Вода = Шторм)
- 💎 + 🔥 = ⚡ (Кристал + Вогонь = Енергія)

## 🛠️ Технічна архітектура

### Технології
- **HTML5** з семантичною розміткою
- **CSS3** з Flexbox/Grid та custom properties
- **JavaScript ES2020+** з модулями та async/await
- **Canvas 2D API** для рендерингу
- **Web Audio API** для процедурного аудіо

### Вбудовані бібліотеки
- **Tone.js v14.7.77** - Аудіо синтез та музика
- **TinyColor2 v1.6.0** - Маніпуляція кольорами
- **GSAP v3.12.2** - Високопродуктивні анімації
- **Dexie.js v3.2.4** - IndexedDB для збереження даних

### Структура проекту
```
/
├── index.html              # Головна сторінка
├── manifest.json          # PWA маніфест
├── sw.js                  # Service Worker
├── libs/                  # Локальні бібліотеки
│   ├── tone.min.js
│   ├── tinycolor.min.js
│   ├── gsap.min.js
│   └── dexie.min.js
├── src/                   # Вихідний код
│   ├── core/             # Основна логіка
│   │   ├── Game.js       # Головний контролер
│   │   ├── Grid.js       # Управління сіткою
│   │   └── Rules.js      # Правила гри
│   ├── graphics/         # Графічна система
│   │   └── Renderer.js   # Canvas рендеринг
│   ├── audio/            # Аудіо система
│   │   └── SoundManager.js
│   ├── ui/               # Інтерфейс користувача
│   │   ├── Interface.js  # UI компоненти
│   │   └── Input.js      # Обробка вводу
│   └── data/             # Збереження даних
│       └── Storage.js    # LocalStorage + IndexedDB
└── tests/                # Юніт-тести
    └── grid.test.js
```

## 🎯 Геймплей

### Основні правила
1. **Мета**: Набрати цільову кількість очок за обмежену кількість ходів
2. **Ходи**: Міняйте місцями сусідні елементи для створення збігів 3+ елементів
3. **Збіги**: Горизонтальні та вертикальні лінії з 3+ однакових елементів
4. **Реакції**: Великі збіги (4+ елементів) можуть викликати хімічні реакції

### Система очок
- **Базовий збіг (3 елементи)**: 30 очок
- **Великий збіг (4-5 елементів)**: 60-100 очок × 2
- **Величезний збіг (6+ елементів)**: 120+ очок × 3
- **Хімічна реакція**: +100 очок × 2.5
- **Каскадний збіг**: +50 очок × 1.5
- **Альтернативна гравітація**: всі очки × 1.2

### Спеціальні механіки
- **Зміна гравітації**: Автоматично кожні 45 секунд або жестом обертання
- **Хімічні реакції**: Збіги 4+ елементів можуть створювати нові елементи
- **Каскадні збіги**: Падіння елементів може створювати нові збіги

## 📱 Мобільна підтримка

### Сенсорне управління
- **Тап**: Вибір елемента
- **Перетягування**: Переміщення елемента
- **Подвійний тап**: Спеціальна дія
- **Жест обертання**: Зміна напрямку гравітації
- **Pinch**: Масштабування (майбутня функція)

### Адаптивність
- **Mobile-first дизайн**: Оптимізовано для екранів від 320px
- **Сенсорні цілі**: Мінімум 44px × 44px для всіх кнопок
- **Підтримка орієнтації**: Автоматичне налаштування при повороті
- **Високий контраст**: Підтримка accessibility налаштувань

## ⌨️ Клавіатурні скорочення

- **Пробіл/Escape**: Пауза/продовження
- **H**: Підказка
- **S**: Перемішати сітку
- **M**: Вимкнути/увімкнути звук
- **Стрілки**: Навігація по сітці (accessibility)
- **Enter**: Активація вибраної клітинки

## 🔧 Налаштування

### Аудіо
- Регулювання гучності звукових ефектів
- Регулювання гучності фонової музики
- Загальне вимкнення звуку

### Геймплей
- Автоматичне збереження кожні 10 секунд
- Збереження при зміні видимості вкладки
- Автоматична пауза при втраті фокусу

## 🚀 Запуск та розробка

### Локальний запуск
1. Відкрийте `index.html` у веб-браузері
2. Або запустіть локальний сервер:
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Node.js (з http-server)
   npx http-server
   
   # PHP
   php -S localhost:8000
   ```

### Тестування
Відкрийте консоль браузера та виконайте:
```javascript
// Запуск тестів сітки
window.runGridTests();
```

### PWA встановлення
1. Відкрийте гру в підтримуваному браузері
2. Натисніть "Додати на головний екран" або "Встановити"
3. Гра буде доступна як нативний додаток

## 📊 Продуктивність

### Цілі
- **60 FPS** на середньобюджетних мобільних пристроях
- **Час завантаження** < 3 секунд на 3G
- **Розмір пакету** < 500KB (стиснутий)
- **Час відгуку** < 100ms на дотик

### Оптимізації
- Кешування зображень елементів
- Об'єднання анімацій в requestAnimationFrame
- Lazy loading для неосновних компонентів
- Стиснення та мінімізація ресурсів

## 🔒 Безпека

### Content Security Policy
```html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-inline'; 
               style-src 'self' 'unsafe-inline'; 
               img-src 'self' data:;">
```

### Валідація даних
- Контрольні суми для збережених файлів
- Валідація вводу користувача
- Захист від XSS через CSP

## 🌐 Сумісність

### Підтримувані браузери
- **Chrome/Edge**: 90+
- **Safari**: 14+
- **Firefox**: 88+
- **Samsung Internet**: 14+

### Android WebView
- **Мінімальна версія**: Android 7.0 (API 24)
- **Рекомендована**: Android 10.0 (API 29)+
- **WebView версія**: 90+

## 📝 Ліцензія

Цей проект створено як демонстрація можливостей Augment Agent.
Код доступний для навчальних та некомерційних цілей.

## 🤝 Внесок

Проект створено Augment Agent з використанням:
- Claude Sonnet 4 (Anthropic)
- Augment Code Context Engine
- Сучасні веб-стандарти та найкращі практики

---

**Версія**: 1.0.0  
**Дата створення**: Серпень 2025  
**Мова інтерфейсу**: Українська
