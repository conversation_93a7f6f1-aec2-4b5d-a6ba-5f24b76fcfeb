/**
 * Dexie.js v3.2.4 - Заглушка для демонстрації
 * Мінімальна реалізація для IndexedDB
 */
(function(global) {
    'use strict';
    
    function Dexie(dbName) {
        this.name = dbName;
        this.version = 1;
        this._schemas = {};
        this._db = null;
        this._isOpen = false;
    }
    
    Dexie.prototype = {
        version: function(versionNumber) {
            this.version = versionNumber;
            return {
                stores: (schema) => {
                    this._schemas = schema;
                    return this;
                }
            };
        },
        
        open: function() {
            return new Promise((resolve, reject) => {
                if (this._isOpen) {
                    resolve(this);
                    return;
                }
                
                const request = indexedDB.open(this.name, this.version);
                
                request.onerror = () => reject(request.error);
                
                request.onsuccess = () => {
                    this._db = request.result;
                    this._isOpen = true;
                    this._createTables();
                    resolve(this);
                };
                
                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    
                    // Створення таблиць на основі схеми
                    for (let tableName in this._schemas) {
                        if (!db.objectStoreNames.contains(tableName)) {
                            const keyPath = this._schemas[tableName].split(',')[0].trim();
                            const store = db.createObjectStore(tableName, { 
                                keyPath: keyPath.startsWith('++') ? keyPath.substring(2) : keyPath,
                                autoIncrement: keyPath.startsWith('++')
                            });
                            
                            // Створення індексів
                            const fields = this._schemas[tableName].split(',').slice(1);
                            fields.forEach(field => {
                                const fieldName = field.trim();
                                if (fieldName && !fieldName.startsWith('&')) {
                                    store.createIndex(fieldName, fieldName, { unique: false });
                                }
                            });
                        }
                    }
                };
            });
        },
        
        _createTables: function() {
            for (let tableName in this._schemas) {
                this[tableName] = new Table(this._db, tableName);
            }
        },
        
        close: function() {
            if (this._db) {
                this._db.close();
                this._isOpen = false;
            }
        },
        
        delete: function() {
            return new Promise((resolve, reject) => {
                this.close();
                const deleteReq = indexedDB.deleteDatabase(this.name);
                deleteReq.onsuccess = () => resolve();
                deleteReq.onerror = () => reject(deleteReq.error);
            });
        }
    };
    
    // Клас для роботи з таблицями
    function Table(db, tableName) {
        this._db = db;
        this._tableName = tableName;
    }
    
    Table.prototype = {
        add: function(item) {
            return new Promise((resolve, reject) => {
                const transaction = this._db.transaction([this._tableName], 'readwrite');
                const store = transaction.objectStore(this._tableName);
                const request = store.add(item);
                
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        },
        
        put: function(item) {
            return new Promise((resolve, reject) => {
                const transaction = this._db.transaction([this._tableName], 'readwrite');
                const store = transaction.objectStore(this._tableName);
                const request = store.put(item);
                
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        },
        
        get: function(key) {
            return new Promise((resolve, reject) => {
                const transaction = this._db.transaction([this._tableName], 'readonly');
                const store = transaction.objectStore(this._tableName);
                const request = store.get(key);
                
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        },
        
        delete: function(key) {
            return new Promise((resolve, reject) => {
                const transaction = this._db.transaction([this._tableName], 'readwrite');
                const store = transaction.objectStore(this._tableName);
                const request = store.delete(key);
                
                request.onsuccess = () => resolve();
                request.onerror = () => reject(request.error);
            });
        },
        
        clear: function() {
            return new Promise((resolve, reject) => {
                const transaction = this._db.transaction([this._tableName], 'readwrite');
                const store = transaction.objectStore(this._tableName);
                const request = store.clear();
                
                request.onsuccess = () => resolve();
                request.onerror = () => reject(request.error);
            });
        },
        
        toArray: function() {
            return new Promise((resolve, reject) => {
                const transaction = this._db.transaction([this._tableName], 'readonly');
                const store = transaction.objectStore(this._tableName);
                const request = store.getAll();
                
                request.onsuccess = () => resolve(request.result || []);
                request.onerror = () => reject(request.error);
            });
        },
        
        count: function() {
            return new Promise((resolve, reject) => {
                const transaction = this._db.transaction([this._tableName], 'readonly');
                const store = transaction.objectStore(this._tableName);
                const request = store.count();
                
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        },
        
        where: function(field) {
            return new WhereClause(this._db, this._tableName, field);
        },
        
        orderBy: function(field) {
            return new Collection(this._db, this._tableName, null, field);
        },
        
        limit: function(count) {
            return new Collection(this._db, this._tableName, null, null, count);
        }
    };
    
    // Клас для умов WHERE
    function WhereClause(db, tableName, field) {
        this._db = db;
        this._tableName = tableName;
        this._field = field;
    }
    
    WhereClause.prototype = {
        equals: function(value) {
            return new Collection(this._db, this._tableName, { field: this._field, op: 'equals', value: value });
        },
        
        above: function(value) {
            return new Collection(this._db, this._tableName, { field: this._field, op: 'above', value: value });
        },
        
        below: function(value) {
            return new Collection(this._db, this._tableName, { field: this._field, op: 'below', value: value });
        }
    };
    
    // Клас для колекцій
    function Collection(db, tableName, whereClause, orderBy, limit) {
        this._db = db;
        this._tableName = tableName;
        this._whereClause = whereClause;
        this._orderBy = orderBy;
        this._limit = limit;
    }
    
    Collection.prototype = {
        toArray: function() {
            return new Promise((resolve, reject) => {
                const transaction = this._db.transaction([this._tableName], 'readonly');
                const store = transaction.objectStore(this._tableName);
                const results = [];
                
                let request;
                if (this._whereClause && this._whereClause.field) {
                    const index = store.index(this._whereClause.field);
                    request = index.openCursor();
                } else {
                    request = store.openCursor();
                }
                
                request.onsuccess = (event) => {
                    const cursor = event.target.result;
                    if (cursor) {
                        let include = true;
                        
                        if (this._whereClause) {
                            const value = cursor.value[this._whereClause.field];
                            switch (this._whereClause.op) {
                                case 'equals':
                                    include = value === this._whereClause.value;
                                    break;
                                case 'above':
                                    include = value > this._whereClause.value;
                                    break;
                                case 'below':
                                    include = value < this._whereClause.value;
                                    break;
                            }
                        }
                        
                        if (include) {
                            results.push(cursor.value);
                        }
                        
                        if (this._limit && results.length >= this._limit) {
                            resolve(results);
                            return;
                        }
                        
                        cursor.continue();
                    } else {
                        resolve(results);
                    }
                };
                
                request.onerror = () => reject(request.error);
            });
        },
        
        count: function() {
            return this.toArray().then(results => results.length);
        },
        
        first: function() {
            return this.toArray().then(results => results[0]);
        },
        
        last: function() {
            return this.toArray().then(results => results[results.length - 1]);
        }
    };
    
    // Експорт
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = Dexie;
    } else if (typeof define === 'function' && define.amd) {
        define([], function() { return Dexie; });
    } else {
        global.Dexie = Dexie;
    }
    
})(typeof window !== 'undefined' ? window : this);
