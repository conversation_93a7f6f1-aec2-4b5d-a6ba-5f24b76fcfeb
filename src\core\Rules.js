/**
 * Клас Rules - управління правилами гри та підрахунком очок
 * Реалізує логіку валідації ходів та обчислення результатів
 */

export class Rules {
    constructor(grid, config) {
        this.grid = grid;
        this.config = config;
        
        // Множники очок для різних типів збігів
        this.scoreMultipliers = {
            basic: 1,           // Звичайний збіг 3 елементи
            large: 2,           // Збіг 4-5 елементів
            huge: 3,            // Збіг 6+ елементів
            combo: 1.5,         // Каскадний збіг
            chemistry: 2.5,     // Хімічна реакція
            gravity: 1.2        // Бонус за гравітацію
        };
        
        // Бонусні очки за спеціальні досягнення
        this.bonusPoints = {
            firstMove: 50,
            perfectClear: 500,
            chainReaction: 100,
            elementMaster: 200,
            gravityMaster: 150
        };
        
        // Лічильники для досягнень
        this.achievements = {
            totalMatches: 0,
            chemicalReactions: 0,
            cascadeChains: 0,
            gravityChanges: 0,
            perfectMoves: 0
        };
    }
    
    /**
     * Перевірка валідності ходу
     */
    isValidMove(fromX, fromY, toX, toY) {
        // Перевірка координат
        if (!this.grid.isValidPosition(fromX, fromY) || 
            !this.grid.isValidPosition(toX, toY)) {
            return false;
        }
        
        // Перевірка що клітинки не порожні
        if (!this.grid.cells[fromX][fromY].element || 
            !this.grid.cells[toX][toY].element) {
            return false;
        }
        
        // Перевірка що клітинки сусідні
        if (!this.grid.areAdjacent(fromX, fromY, toX, toY)) {
            return false;
        }
        
        // Перевірка що хід не анімується
        if (this.grid.cells[fromX][fromY].isAnimating || 
            this.grid.cells[toX][toY].isAnimating) {
            return false;
        }
        
        // Симуляція ходу для перевірки збігів
        return this.simulateMove(fromX, fromY, toX, toY);
    }
    
    /**
     * Симуляція ходу для перевірки результату
     */
    simulateMove(fromX, fromY, toX, toY) {
        // Збереження поточного стану
        const fromElement = this.grid.cells[fromX][fromY].element;
        const toElement = this.grid.cells[toX][toY].element;
        
        // Тимчасовий обмін
        this.grid.cells[fromX][fromY].element = toElement;
        this.grid.cells[toX][toY].element = fromElement;
        
        // Перевірка збігів
        const hasMatches = this.checkForMatches(fromX, fromY) || 
                          this.checkForMatches(toX, toY);
        
        // Відновлення стану
        this.grid.cells[fromX][fromY].element = fromElement;
        this.grid.cells[toX][toY].element = toElement;
        
        return hasMatches;
    }
    
    /**
     * Перевірка збігів в конкретній позиції
     */
    checkForMatches(x, y) {
        const element = this.grid.cells[x][y].element;
        if (!element) return false;
        
        // Перевірка горизонтальних збігів
        let horizontalCount = 1;
        
        // Ліворуч
        for (let i = x - 1; i >= 0; i--) {
            if (this.grid.cells[i][y].element && 
                this.grid.cells[i][y].element.id === element.id) {
                horizontalCount++;
            } else {
                break;
            }
        }
        
        // Праворуч
        for (let i = x + 1; i < this.grid.size; i++) {
            if (this.grid.cells[i][y].element && 
                this.grid.cells[i][y].element.id === element.id) {
                horizontalCount++;
            } else {
                break;
            }
        }
        
        if (horizontalCount >= 3) return true;
        
        // Перевірка вертикальних збігів
        let verticalCount = 1;
        
        // Вгору
        for (let i = y - 1; i >= 0; i--) {
            if (this.grid.cells[x][i].element && 
                this.grid.cells[x][i].element.id === element.id) {
                verticalCount++;
            } else {
                break;
            }
        }
        
        // Вниз
        for (let i = y + 1; i < this.grid.size; i++) {
            if (this.grid.cells[x][i].element && 
                this.grid.cells[x][i].element.id === element.id) {
                verticalCount++;
            } else {
                break;
            }
        }
        
        return verticalCount >= 3;
    }
    
    /**
     * Обчислення очок за результат ходу
     */
    calculateScore(moveResult) {
        let totalScore = 0;
        let multiplier = 1;
        
        // Базові очки за збіги
        if (moveResult.matches) {
            for (const match of moveResult.matches) {
                let matchScore = this.calculateMatchScore(match);
                
                // Застосування множників
                if (match.length >= 6) {
                    matchScore *= this.scoreMultipliers.huge;
                } else if (match.length >= 4) {
                    matchScore *= this.scoreMultipliers.large;
                } else {
                    matchScore *= this.scoreMultipliers.basic;
                }
                
                totalScore += matchScore;
                this.achievements.totalMatches++;
            }
        }
        
        // Бонус за хімічні реакції
        if (moveResult.reactions && moveResult.reactions.length > 0) {
            const reactionBonus = moveResult.reactions.length * 
                                this.config.chemistryBonus * 
                                this.scoreMultipliers.chemistry;
            totalScore += reactionBonus;
            this.achievements.chemicalReactions += moveResult.reactions.length;
            
            console.log(`Бонус за хімічні реакції: ${reactionBonus}`);
        }
        
        // Бонус за каскадні збіги
        if (moveResult.cascades && moveResult.cascades > 0) {
            const cascadeBonus = moveResult.cascades * 
                               this.config.comboBonus * 
                               this.scoreMultipliers.combo;
            totalScore += cascadeBonus;
            this.achievements.cascadeChains++;
            
            console.log(`Бонус за каскади: ${cascadeBonus}`);
        }
        
        // Бонус за гравітацію
        if (this.grid.gravityDirection !== 'down') {
            totalScore *= this.scoreMultipliers.gravity;
            console.log(`Бонус за альтернативну гравітацію: x${this.scoreMultipliers.gravity}`);
        }
        
        // Бонус за ідеальний хід (збіг + реакція + каскад)
        if (moveResult.matches && moveResult.reactions && moveResult.cascades) {
            totalScore += this.bonusPoints.perfectClear;
            this.achievements.perfectMoves++;
            console.log(`Бонус за ідеальний хід: ${this.bonusPoints.perfectClear}`);
        }
        
        // Застосування загального множника рівня
        const levelMultiplier = 1 + (this.getCurrentLevel() - 1) * 0.1;
        totalScore = Math.floor(totalScore * levelMultiplier);
        
        return totalScore;
    }
    
    /**
     * Обчислення очок за окремий збіг
     */
    calculateMatchScore(match) {
        const baseScore = this.config.scoreMultiplier;
        let score = match.length * baseScore;
        
        // Бонус за форму збігу
        const shape = this.analyzeMatchShape(match);
        switch (shape) {
            case 'L':
                score *= 1.5;
                break;
            case 'T':
                score *= 2;
                break;
            case 'cross':
                score *= 2.5;
                break;
            case 'square':
                score *= 3;
                break;
        }
        
        return Math.floor(score);
    }
    
    /**
     * Аналіз форми збігу для додаткових бонусів
     */
    analyzeMatchShape(match) {
        if (match.length < 4) return 'line';
        
        // Сортування позицій
        const positions = match.map(m => ({ x: m.x, y: m.y }));
        positions.sort((a, b) => a.x - b.x || a.y - b.y);
        
        // Перевірка на різні форми
        if (this.isLShape(positions)) return 'L';
        if (this.isTShape(positions)) return 'T';
        if (this.isCrossShape(positions)) return 'cross';
        if (this.isSquareShape(positions)) return 'square';
        
        return 'line';
    }
    
    /**
     * Перевірка L-подібної форми
     */
    isLShape(positions) {
        if (positions.length < 4) return false;
        
        // Простий алгоритм перевірки L-форми
        const xCoords = [...new Set(positions.map(p => p.x))];
        const yCoords = [...new Set(positions.map(p => p.y))];
        
        return xCoords.length === 2 && yCoords.length === 2;
    }
    
    /**
     * Перевірка T-подібної форми
     */
    isTShape(positions) {
        if (positions.length < 4) return false;
        
        // Пошук центральної точки з максимальною кількістю сусідів
        for (const pos of positions) {
            let neighbors = 0;
            for (const other of positions) {
                if (pos !== other && this.grid.areAdjacent(pos.x, pos.y, other.x, other.y)) {
                    neighbors++;
                }
            }
            if (neighbors >= 3) return true;
        }
        
        return false;
    }
    
    /**
     * Перевірка хрестоподібної форми
     */
    isCrossShape(positions) {
        if (positions.length < 5) return false;
        
        // Пошук центру з 4 сусідами
        for (const pos of positions) {
            let neighbors = 0;
            const directions = [
                { dx: -1, dy: 0 }, { dx: 1, dy: 0 },
                { dx: 0, dy: -1 }, { dx: 0, dy: 1 }
            ];
            
            for (const dir of directions) {
                const neighborPos = { x: pos.x + dir.dx, y: pos.y + dir.dy };
                if (positions.some(p => p.x === neighborPos.x && p.y === neighborPos.y)) {
                    neighbors++;
                }
            }
            
            if (neighbors === 4) return true;
        }
        
        return false;
    }
    
    /**
     * Перевірка квадратної форми
     */
    isSquareShape(positions) {
        if (positions.length !== 4) return false;
        
        // Перевірка що всі позиції утворюють квадрат 2x2
        const minX = Math.min(...positions.map(p => p.x));
        const minY = Math.min(...positions.map(p => p.y));
        
        const expectedPositions = [
            { x: minX, y: minY },
            { x: minX + 1, y: minY },
            { x: minX, y: minY + 1 },
            { x: minX + 1, y: minY + 1 }
        ];
        
        return expectedPositions.every(expected => 
            positions.some(pos => pos.x === expected.x && pos.y === expected.y)
        );
    }
    
    /**
     * Перевірка можливих ходів на сітці
     */
    findPossibleMoves() {
        const possibleMoves = [];
        
        for (let x = 0; x < this.grid.size; x++) {
            for (let y = 0; y < this.grid.size; y++) {
                // Перевірка ходів праворуч та вниз
                const directions = [
                    { dx: 1, dy: 0 },  // праворуч
                    { dx: 0, dy: 1 }   // вниз
                ];
                
                for (const dir of directions) {
                    const toX = x + dir.dx;
                    const toY = y + dir.dy;
                    
                    if (this.isValidMove(x, y, toX, toY)) {
                        possibleMoves.push({
                            from: { x, y },
                            to: { x: toX, y: toY },
                            score: this.estimateMoveScore(x, y, toX, toY)
                        });
                    }
                }
            }
        }
        
        // Сортування за потенційним рахунком
        possibleMoves.sort((a, b) => b.score - a.score);
        
        return possibleMoves;
    }
    
    /**
     * Оцінка потенційного рахунку за хід
     */
    estimateMoveScore(fromX, fromY, toX, toY) {
        // Симуляція ходу
        const fromElement = this.grid.cells[fromX][fromY].element;
        const toElement = this.grid.cells[toX][toY].element;
        
        this.grid.cells[fromX][fromY].element = toElement;
        this.grid.cells[toX][toY].element = fromElement;
        
        // Підрахунок потенційних збігів
        let estimatedScore = 0;
        const matches = this.grid.findAllMatches();
        
        for (const match of matches) {
            estimatedScore += this.calculateMatchScore(match);
        }
        
        // Відновлення стану
        this.grid.cells[fromX][fromY].element = fromElement;
        this.grid.cells[toX][toY].element = toElement;
        
        return estimatedScore;
    }
    
    /**
     * Надання підказки гравцю
     */
    getHint() {
        const possibleMoves = this.findPossibleMoves();
        
        if (possibleMoves.length === 0) {
            return null; // Немає можливих ходів
        }
        
        // Повернення найкращого ходу
        return possibleMoves[0];
    }
    
    /**
     * Перевірка на deadlock (відсутність можливих ходів)
     */
    isDeadlock() {
        return this.findPossibleMoves().length === 0;
    }
    
    /**
     * Генерація нових елементів після перемішування
     */
    shuffleGrid() {
        const elements = [];
        
        // Збір всіх елементів з сітки
        for (let x = 0; x < this.grid.size; x++) {
            for (let y = 0; y < this.grid.size; y++) {
                if (this.grid.cells[x][y].element) {
                    elements.push(this.grid.cells[x][y].element);
                }
            }
        }
        
        // Перемішування масиву елементів
        for (let i = elements.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [elements[i], elements[j]] = [elements[j], elements[i]];
        }
        
        // Розміщення елементів назад на сітці
        let elementIndex = 0;
        for (let x = 0; x < this.grid.size; x++) {
            for (let y = 0; y < this.grid.size; y++) {
                if (elementIndex < elements.length) {
                    this.grid.cells[x][y].element = elements[elementIndex];
                    elementIndex++;
                }
            }
        }
        
        console.log('Сітка перемішана');
    }
    
    /**
     * Отримання поточного рівня (може бути переданий ззовні)
     */
    getCurrentLevel() {
        // Заглушка - в реальній грі це буде передаватися з Game
        return 1;
    }
    
    /**
     * Отримання статистики досягнень
     */
    getAchievements() {
        return { ...this.achievements };
    }
    
    /**
     * Скидання статистики досягнень
     */
    resetAchievements() {
        this.achievements = {
            totalMatches: 0,
            chemicalReactions: 0,
            cascadeChains: 0,
            gravityChanges: 0,
            perfectMoves: 0
        };
    }
    
    /**
     * Валідація цілісності сітки
     */
    validateGrid() {
        let isValid = true;
        const errors = [];
        
        // Перевірка що всі клітинки мають валідні елементи
        for (let x = 0; x < this.grid.size; x++) {
            for (let y = 0; y < this.grid.size; y++) {
                const cell = this.grid.cells[x][y];
                
                if (!cell.element) {
                    errors.push(`Порожня клітинка в позиції (${x}, ${y})`);
                    isValid = false;
                }
                
                if (cell.element && !this.grid.elementArray.includes(cell.element)) {
                    errors.push(`Невідомий елемент в позиції (${x}, ${y})`);
                    isValid = false;
                }
            }
        }
        
        return { isValid, errors };
    }
}
