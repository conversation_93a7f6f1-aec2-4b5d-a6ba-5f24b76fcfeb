<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест гри "Хімічні Елементи"</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #2980b9;
        }
        
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        #gameFrame {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Тестування гри "Хімічні Елементи"</h1>
        
        <div class="test-section">
            <h3>1. Тест завантаження модулів</h3>
            <button class="test-button" onclick="testModuleLoading()">Тест модулів</button>
            <div id="moduleResult" class="test-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. Тест ігрової сітки</h3>
            <button class="test-button" onclick="testGrid()">Тест Grid</button>
            <div id="gridResult" class="test-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. Тест аудіо системи</h3>
            <button class="test-button" onclick="testAudio()">Тест Audio</button>
            <button class="test-button" onclick="playTestSound()">Тестовий звук</button>
            <div id="audioResult" class="test-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>4. Тест рендерингу</h3>
            <button class="test-button" onclick="testRenderer()">Тест Renderer</button>
            <div id="rendererResult" class="test-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>5. Тест збереження даних</h3>
            <button class="test-button" onclick="testStorage()">Тест Storage</button>
            <div id="storageResult" class="test-result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>6. Повний тест гри</h3>
            <button class="test-button" onclick="loadFullGame()">Завантажити гру</button>
            <div id="gameResult" class="test-result" style="display: none;"></div>
            <iframe id="gameFrame" src="about:blank" style="display: none;"></iframe>
        </div>
    </div>

    <script>
        // Глобальні змінні для тестування
        let testResults = {};
        
        /**
         * Утилітарна функція для відображення результатів
         */
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `test-result ${type}`;
            element.textContent = message;
        }
        
        /**
         * Тест завантаження модулів
         */
        async function testModuleLoading() {
            try {
                showResult('moduleResult', 'Завантаження модулів...', 'info');
                
                // Тест завантаження основних модулів
                const modules = [
                    './src/core/Game.js',
                    './src/core/Grid.js',
                    './src/core/Rules.js',
                    './src/graphics/Renderer.js',
                    './src/audio/SoundManager.js'
                ];
                
                const results = [];
                
                for (const modulePath of modules) {
                    try {
                        const module = await import(modulePath);
                        results.push(`✅ ${modulePath}: OK`);
                    } catch (error) {
                        results.push(`❌ ${modulePath}: ${error.message}`);
                    }
                }
                
                showResult('moduleResult', results.join('\n'), 'success');
                testResults.modules = true;
                
            } catch (error) {
                showResult('moduleResult', `Помилка: ${error.message}`, 'error');
                testResults.modules = false;
            }
        }
        
        /**
         * Тест ігрової сітки
         */
        async function testGrid() {
            try {
                showResult('gridResult', 'Тестування ігрової сітки...', 'info');
                
                const { Grid } = await import('./src/core/Grid.js');
                const grid = new Grid(8, 6);
                
                await grid.initialize();
                
                const tests = [
                    () => grid.size === 8 ? '✅ Розмір сітки: OK' : '❌ Розмір сітки: FAIL',
                    () => grid.elementTypes === 6 ? '✅ Типи елементів: OK' : '❌ Типи елементів: FAIL',
                    () => grid.cells.length === 8 ? '✅ Клітинки створені: OK' : '❌ Клітинки створені: FAIL',
                    () => grid.isValidPosition(0, 0) ? '✅ Валідація позицій: OK' : '❌ Валідація позицій: FAIL',
                    () => grid.areAdjacent(0, 0, 0, 1) ? '✅ Перевірка сусідності: OK' : '❌ Перевірка сусідності: FAIL'
                ];
                
                const results = tests.map(test => test());
                
                await grid.generateNewGrid();
                results.push('✅ Генерація сітки: OK');
                
                showResult('gridResult', results.join('\n'), 'success');
                testResults.grid = true;
                
            } catch (error) {
                showResult('gridResult', `Помилка: ${error.message}`, 'error');
                testResults.grid = false;
            }
        }
        
        /**
         * Тест аудіо системи
         */
        async function testAudio() {
            try {
                showResult('audioResult', 'Тестування аудіо системи...', 'info');
                
                const { SoundManager } = await import('./src/audio/SoundManager.js');
                const soundManager = new SoundManager();
                
                await soundManager.initialize();
                
                const tests = [
                    () => soundManager.isInitialized ? '✅ Ініціалізація: OK' : '❌ Ініціалізація: FAIL',
                    () => soundManager.audioContext ? '✅ AudioContext: OK' : '❌ AudioContext: FAIL',
                    () => soundManager.masterGain ? '✅ Master Gain: OK' : '❌ Master Gain: FAIL'
                ];
                
                const results = tests.map(test => test());
                
                showResult('audioResult', results.join('\n'), 'success');
                testResults.audio = true;
                
            } catch (error) {
                showResult('audioResult', `Помилка: ${error.message}`, 'error');
                testResults.audio = false;
            }
        }
        
        /**
         * Відтворення тестового звуку
         */
        async function playTestSound() {
            try {
                const { SoundManager } = await import('./src/audio/SoundManager.js');
                const soundManager = new SoundManager();
                await soundManager.initialize();
                
                soundManager.playSound('button');
                showResult('audioResult', 'Тестовий звук відтворено', 'info');
                
            } catch (error) {
                showResult('audioResult', `Помилка відтворення: ${error.message}`, 'error');
            }
        }
        
        /**
         * Тест системи рендерингу
         */
        async function testRenderer() {
            try {
                showResult('rendererResult', 'Тестування системи рендерингу...', 'info');
                
                // Створення тестового canvas
                const canvas = document.createElement('canvas');
                canvas.width = 400;
                canvas.height = 400;
                canvas.id = 'testCanvas';
                
                const { Renderer } = await import('./src/graphics/Renderer.js');
                
                // Тимчасово додаємо canvas до DOM
                document.body.appendChild(canvas);
                
                const renderer = new Renderer();
                
                // Підміна getElementById для тесту
                const originalGetElementById = document.getElementById;
                document.getElementById = (id) => {
                    if (id === 'gameCanvas') return canvas;
                    return originalGetElementById.call(document, id);
                };
                
                await renderer.initialize();
                
                const tests = [
                    () => renderer.canvas ? '✅ Canvas: OK' : '❌ Canvas: FAIL',
                    () => renderer.ctx ? '✅ Context: OK' : '❌ Context: FAIL',
                    () => renderer.elementCache ? '✅ Element Cache: OK' : '❌ Element Cache: FAIL'
                ];
                
                const results = tests.map(test => test());
                
                // Очищення
                document.body.removeChild(canvas);
                document.getElementById = originalGetElementById;
                
                showResult('rendererResult', results.join('\n'), 'success');
                testResults.renderer = true;
                
            } catch (error) {
                showResult('rendererResult', `Помилка: ${error.message}`, 'error');
                testResults.renderer = false;
            }
        }
        
        /**
         * Тест системи збереження
         */
        async function testStorage() {
            try {
                showResult('storageResult', 'Тестування системи збереження...', 'info');
                
                const { Storage } = await import('./src/data/Storage.js');
                const storage = new Storage();
                
                await storage.initialize();
                
                // Тест збереження налаштувань
                const testSettings = { volume: 0.5, theme: 'dark' };
                await storage.saveSettings(testSettings);
                
                const loadedSettings = await storage.getSettings();
                
                const tests = [
                    () => storage.dbName ? '✅ DB Name: OK' : '❌ DB Name: FAIL',
                    () => loadedSettings ? '✅ Settings Save/Load: OK' : '❌ Settings Save/Load: FAIL',
                    () => loadedSettings && loadedSettings.volume === 0.5 ? '✅ Settings Data: OK' : '❌ Settings Data: FAIL'
                ];
                
                const results = tests.map(test => test());
                
                showResult('storageResult', results.join('\n'), 'success');
                testResults.storage = true;
                
            } catch (error) {
                showResult('storageResult', `Помилка: ${error.message}`, 'error');
                testResults.storage = false;
            }
        }
        
        /**
         * Завантаження повної гри
         */
        function loadFullGame() {
            try {
                showResult('gameResult', 'Завантаження повної гри...', 'info');
                
                const gameFrame = document.getElementById('gameFrame');
                gameFrame.src = './index.html';
                gameFrame.style.display = 'block';
                
                gameFrame.onload = () => {
                    showResult('gameResult', '✅ Гра завантажена успішно!', 'success');
                };
                
                gameFrame.onerror = () => {
                    showResult('gameResult', '❌ Помилка завантаження гри', 'error');
                };
                
            } catch (error) {
                showResult('gameResult', `Помилка: ${error.message}`, 'error');
            }
        }
        
        /**
         * Автоматичний запуск базових тестів
         */
        window.addEventListener('load', () => {
            console.log('🧪 Тестова сторінка завантажена');
            console.log('Натисніть кнопки для запуску тестів');
        });
    </script>
</body>
</html>
