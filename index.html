<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="description" content="Інноваційна HTML5 гра-головоломка 'три в ряд' з унікальними механіками хімії елементів">
    <meta name="keywords" content="гра, головоломка, три в ряд, HTML5, хімія">
    <meta name="author" content="Augment Agent">
    <meta name="theme-color" content="#2c3e50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Хімічні Елементи">
    <meta name="mobile-web-app-capable" content="yes">
    
    <!-- PWA маніфест -->
    <link rel="manifest" href="manifest.json">
    
    <!-- Іконки для різних пристроїв -->
    <link rel="apple-touch-icon" sizes="180x180" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDE4MCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxODAiIGhlaWdodD0iMTgwIiBmaWxsPSIjMmMzZTUwIi8+CjxjaXJjbGUgY3g9IjkwIiBjeT0iOTAiIHI9IjQwIiBmaWxsPSIjZTc0YzNjIi8+CjxjaXJjbGUgY3g9IjkwIiBjeT0iOTAiIHI9IjIwIiBmaWxsPSIjZjM5YzEyIi8+Cjwvc3ZnPgo=">
    
    <title>Хімічні Елементи</title>
    
    <style>
        /* CSS Custom Properties для тематизації */
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #e74c3c;
            --accent-color: #f39c12;
            --background-color: #ecf0f1;
            --text-color: #2c3e50;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
            
            /* Розміри для сенсорних цілей */
            --touch-target-size: 44px;
            --border-radius: 8px;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
            
            /* Анімації */
            --transition-fast: 0.15s ease-out;
            --transition-normal: 0.3s ease-out;
            --transition-slow: 0.5s ease-out;
        }
        
        /* Глобальні стилі та скидання */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        html, body {
            height: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: var(--background-color);
            color: var(--text-color);
            overflow: hidden;
            touch-action: manipulation;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        
        /* Основний контейнер гри */
        #gameContainer {
            display: flex;
            flex-direction: column;
            height: 100vh;
            width: 100vw;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* Заголовок гри */
        #gameHeader {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            min-height: var(--touch-target-size);
        }
        
        #gameTitle {
            font-size: 1.2rem;
            font-weight: 600;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        
        #gameStats {
            display: flex;
            gap: 15px;
            color: white;
            font-size: 0.9rem;
        }
        
        /* Основна ігрова область */
        #gameBoard {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            position: relative;
        }
        
        /* Canvas для рендерингу гри */
        #gameCanvas {
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            background: rgba(255, 255, 255, 0.9);
            touch-action: none;
            max-width: 100%;
            max-height: 100%;
        }
        
        /* Панель управління */
        #gameControls {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            gap: 15px;
        }
        
        /* Кнопки управління */
        .control-btn {
            min-width: var(--touch-target-size);
            min-height: var(--touch-target-size);
            border: none;
            border-radius: var(--border-radius);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 1rem;
            cursor: pointer;
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 15px;
        }
        
        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }
        
        .control-btn:active {
            transform: translateY(0);
            background: rgba(255, 255, 255, 0.4);
        }
        
        /* Індикатор завантаження */
        #loadingScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            transition: opacity var(--transition-slow);
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            color: white;
            font-size: 1.1rem;
            text-align: center;
        }
        
        /* Адаптивність для мобільних пристроїв */
        @media (max-width: 768px) {
            #gameHeader {
                padding: 8px 15px;
            }
            
            #gameTitle {
                font-size: 1rem;
            }
            
            #gameStats {
                font-size: 0.8rem;
                gap: 10px;
            }
            
            #gameBoard {
                padding: 15px;
            }
            
            #gameControls {
                padding: 10px 15px;
                gap: 10px;
            }
            
            .control-btn {
                padding: 0 12px;
                font-size: 0.9rem;
            }
        }
        
        /* Підтримка високого контрасту */
        @media (prefers-contrast: high) {
            :root {
                --background-color: #000000;
                --text-color: #ffffff;
                --primary-color: #ffffff;
                --secondary-color: #ffff00;
            }
        }
        
        /* Підтримка зменшеної анімації */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
        
        /* Орієнтація екрану */
        @media (orientation: landscape) and (max-height: 500px) {
            #gameHeader {
                padding: 5px 15px;
                min-height: 35px;
            }
            
            #gameTitle {
                font-size: 0.9rem;
            }
            
            #gameControls {
                padding: 8px 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Екран завантаження -->
    <div id="loadingScreen">
        <div class="loading-spinner"></div>
        <div class="loading-text">
            <div>Завантаження гри...</div>
            <div style="font-size: 0.9rem; margin-top: 10px; opacity: 0.8;">Підготовка хімічних елементів</div>
        </div>
    </div>
    
    <!-- Основний контейнер гри -->
    <div id="gameContainer">
        <!-- Заголовок з інформацією -->
        <header id="gameHeader">
            <h1 id="gameTitle">Хімічні Елементи</h1>
            <div id="gameStats">
                <span id="scoreDisplay">Очки: 0</span>
                <span id="levelDisplay">Рівень: 1</span>
                <span id="movesDisplay">Ходи: 30</span>
            </div>
        </header>
        
        <!-- Основна ігрова область -->
        <main id="gameBoard">
            <canvas id="gameCanvas" width="400" height="400"></canvas>
        </main>
        
        <!-- Панель управління -->
        <footer id="gameControls">
            <button class="control-btn" id="pauseBtn" title="Пауза">⏸️</button>
            <button class="control-btn" id="hintBtn" title="Підказка">💡</button>
            <button class="control-btn" id="shuffleBtn" title="Перемішати">🔄</button>
            <button class="control-btn" id="settingsBtn" title="Налаштування">⚙️</button>
        </footer>
    </div>
    
    <!-- Підключення бібліотек -->
    <script src="libs/tone.min.js"></script>
    <script src="libs/tinycolor.min.js"></script>
    <script src="libs/gsap.min.js"></script>
    <script src="libs/dexie.min.js"></script>
    
    <!-- Основні модулі гри -->
    <script type="module" src="src/core/Game.js"></script>

    <script>
        // Ініціалізація гри після завантаження DOM
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Імпорт основного класу гри
                const { Game } = await import('./src/core/Game.js');

                // Створення екземпляру гри
                window.game = new Game();

                // Ініціалізація гри
                await window.game.initialize();

                // Приховування екрану завантаження
                const loadingScreen = document.getElementById('loadingScreen');
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);

                console.log('Гра успішно ініціалізована');
            } catch (error) {
                console.error('Помилка ініціалізації гри:', error);

                // Показати повідомлення про помилку
                const loadingText = document.querySelector('.loading-text');
                loadingText.innerHTML = `
                    <div style="color: #e74c3c;">Помилка завантаження гри</div>
                    <div style="font-size: 0.8rem; margin-top: 10px;">${error.message}</div>
                `;
            }
        });

        // Обробка зміни розміру вікна
        window.addEventListener('resize', () => {
            if (window.game && window.game.handleResize) {
                window.game.handleResize();
            }
        });

        // Обробка зміни орієнтації
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                if (window.game && window.game.handleResize) {
                    window.game.handleResize();
                }
            }, 100);
        });

        // Обробка втрати/отримання фокусу для автоматичної паузи
        document.addEventListener('visibilitychange', () => {
            if (window.game) {
                if (document.hidden) {
                    window.game.pause();
                } else {
                    // Не відновлюємо автоматично, дозволяємо користувачу вирішити
                }
            }
        });
    </script>
</body>
</html>
